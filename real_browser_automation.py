#!/usr/bin/env python3
"""
Real Browser Automation - Working solution that preserves your login sessions
"""

import asyncio
from browser_use import Agent, <PERSON><PERSON><PERSON>, ChatGoogle
from dotenv import load_dotenv
import os

load_dotenv()

async def automate_with_real_sessions():
    """
    Automate tasks while preserving your real login sessions.
    This approach uses browser-use's built-in browser management but with your profile.
    """
    
    print("🌐 Real Browser Automation with Session Preservation")
    print("=" * 60)
    
    try:
        print("🔧 Setting up browser with your Profile 2...")
        
        # Create browser instance that will preserve your sessions
        browser = Browser(
            # Use your exact profile directory
            user_data_dir=r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data",
            profile_directory="Profile 2",
            
            # Keep browser visible so you can see what's happening
            headless=False,
            
            # Browser settings
            window_size={"width": 1200, "height": 800},
            
            # Keep the browser alive between tasks
            keep_alive=True,
            
            # Additional Chrome arguments for better compatibility
            args=[
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding"
            ]
        )
        
        print("✅ Browser configured with your profile")
        
        # Create LLM
        llm = ChatGoogle(model="gemini-1.5-flash", temperature=0.1)
        print("✅ AI model ready")
        
        # Test 1: Simple navigation
        print("\n📍 Test 1: Navigation to a test site...")
        agent1 = Agent(
            task="Navigate to https://httpbin.org and tell me what the main heading says",
            browser=browser,
            llm=llm
        )
        
        result1 = await agent1.run()
        print(f"✅ Navigation result: {result1}")
        
        # Test 2: Check if your sessions are preserved
        print("\n🔐 Test 2: Checking session preservation...")
        agent2 = Agent(
            task="Navigate to https://accounts.google.com and tell me if I'm already logged in or if I see a login page",
            browser=browser,
            llm=llm
        )
        
        result2 = await agent2.run()
        print(f"✅ Session check result: {result2}")
        
        # Test 3: Interactive task
        print("\n🎯 Test 3: Interactive automation...")
        
        # Ask user what they want to automate
        print("\n" + "=" * 60)
        print("🤖 Your browser is ready! What would you like me to automate?")
        print("Examples:")
        print("  - 'Search for Python tutorials on Google'")
        print("  - 'Go to Gmail and check my inbox'")
        print("  - 'Navigate to Facebook and check notifications'")
        print("  - 'Fill out a form on a website'")
        print("  - Type 'quit' to exit")
        
        while True:
            user_task = input("\n🎯 Enter your automation task: ").strip()
            
            if user_task.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_task:
                continue
            
            print(f"\n🚀 Executing: {user_task}")
            
            try:
                agent = Agent(
                    task=user_task,
                    browser=browser,
                    llm=llm
                )
                
                result = await agent.run()
                print(f"✅ Task completed: {result}")
                
            except Exception as e:
                print(f"❌ Task failed: {e}")
        
        print("\n🎉 Automation session completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def quick_demo():
    """Quick demo of browser automation with your real sessions."""
    
    print("🚀 Quick Demo: Browser Automation with Your Real Sessions")
    print("=" * 60)
    
    try:
        # Simple browser setup
        browser = Browser(
            user_data_dir=r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data",
            profile_directory="Profile 2",
            headless=False,
            keep_alive=True
        )
        
        llm = ChatGoogle(model="gemini-1.5-flash")
        
        # Demo task
        print("🎯 Demo task: Navigate to Google and perform a search")
        
        agent = Agent(
            task="Go to google.com and search for 'browser automation python'. Tell me the first few search results you see.",
            browser=browser,
            llm=llm
        )
        
        result = await agent.run()
        
        print("✅ Demo completed!")
        print(f"🤖 AI Response: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

def main():
    print("🌟 Real Browser Automation Tool")
    print("This tool preserves all your login sessions and cookies!")
    print("=" * 60)
    
    print("\nChoose an option:")
    print("1. Quick Demo (simple automation test)")
    print("2. Full Interactive Session (you control the tasks)")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 Starting Quick Demo...")
        success = asyncio.run(quick_demo())
        if success:
            print("\n🎉 Demo successful! Your real browser sessions are preserved.")
        
    elif choice == "2":
        print("\n🚀 Starting Interactive Session...")
        success = asyncio.run(automate_with_real_sessions())
        if success:
            print("\n🎉 Session completed! All your login sessions remain intact.")
        
    elif choice == "3":
        print("👋 Goodbye!")
        return
    
    else:
        print("❌ Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
