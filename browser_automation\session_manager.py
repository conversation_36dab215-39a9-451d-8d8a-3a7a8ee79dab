"""
Browser session manager with persistent state management.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path

from .config import Config
from .cdp_handler import CDPHandler
from .credential_storage import CredentialStorage
from .exceptions import SessionError, ConnectionError


logger = logging.getLogger(__name__)


class BrowserSessionManager:
    """
    Manages browser sessions with persistent state including cookies,
    local storage, and authentication tokens.
    """
    
    def __init__(self, config: Optional[Config] = None, session_id: Optional[str] = None):
        self.config = config or Config()
        self.session_id = session_id or str(uuid.uuid4())
        
        self.cdp_handler = CDPHandler(self.config)
        self.credential_storage = CredentialStorage(self.config)
        
        self._auto_save_task: Optional[asyncio.Task] = None
        self._session_data: Dict[str, Any] = {}
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        log_config = self.config.logging
        
        # Configure logger
        logger.setLevel(log_config.level)
        
        # Create formatter
        formatter = logging.Formatter(log_config.format)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler if specified
        if log_config.file_path:
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                log_config.file_path,
                maxBytes=log_config.max_file_size,
                backupCount=log_config.backup_count
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
    
    async def connect_to_existing_browser(self) -> bool:
        """
        Connect to an existing browser instance and restore session state.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to existing browser for session: {self.session_id}")
            
            # Connect to browser via CDP
            if not await self.cdp_handler.connect_to_existing_browser():
                logger.warning("Failed to connect to existing browser, trying to launch new one")
                return await self.launch_new_browser()
            
            # Restore session state
            await self._restore_session_state()
            
            # Start auto-save
            await self._start_auto_save()
            
            logger.info("Successfully connected to existing browser and restored session")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to existing browser: {e}")
            return False
    
    async def launch_new_browser(self) -> bool:
        """
        Launch a new browser instance with remote debugging enabled.
        
        Returns:
            bool: True if launch successful, False otherwise
        """
        try:
            logger.info(f"Launching new browser for session: {self.session_id}")
            
            # Launch browser with debugging
            if not await self.cdp_handler.launch_browser_with_debugging():
                raise ConnectionError("Failed to launch browser with debugging")
            
            # Restore session state if available
            await self._restore_session_state()
            
            # Start auto-save
            await self._start_auto_save()
            
            logger.info("Successfully launched new browser and restored session")
            return True
            
        except Exception as e:
            logger.error(f"Failed to launch new browser: {e}")
            return False
    
    async def _restore_session_state(self) -> None:
        """Restore session state from storage."""
        try:
            logger.info("Restoring session state")
            
            # Load session data
            self._session_data = self.credential_storage.load_session_data(self.session_id) or {}
            
            # Restore cookies
            cookies = self.credential_storage.load_cookies(self.session_id)
            if cookies:
                await self.cdp_handler.set_cookies(cookies)
                logger.info(f"Restored {len(cookies)} cookies")
            
            # Restore local storage for known URLs
            local_storage_data = self.credential_storage.load_local_storage(self.session_id)
            if local_storage_data:
                # Note: Local storage restoration requires visiting each URL
                # This will be handled when navigating to specific pages
                logger.info(f"Local storage data available for restoration")
            
            # Load auth tokens
            auth_tokens = self.credential_storage.load_auth_tokens(self.session_id)
            if auth_tokens:
                self._session_data['auth_tokens'] = auth_tokens
                logger.info(f"Restored {len(auth_tokens)} auth tokens")
            
        except Exception as e:
            logger.warning(f"Failed to restore some session state: {e}")
    
    async def save_session_state(self) -> None:
        """Save current session state to storage."""
        try:
            logger.debug("Saving session state")
            
            if not self.cdp_handler.is_connected:
                logger.warning("Cannot save session state - not connected to browser")
                return
            
            # Save cookies
            cookies = await self.cdp_handler.get_cookies()
            self.credential_storage.store_cookies(self.session_id, cookies)
            
            # Save current page's local storage if available
            if self.cdp_handler.page:
                try:
                    current_url = self.cdp_handler.page.url
                    if current_url and not current_url.startswith('chrome://'):
                        local_storage = await self.cdp_handler.get_local_storage(current_url)
                        if local_storage:
                            # Merge with existing local storage data
                            existing_storage = self.credential_storage.load_local_storage(self.session_id) or {}
                            existing_storage[current_url] = local_storage
                            self.credential_storage.store_local_storage(self.session_id, existing_storage)
                except Exception as e:
                    logger.debug(f"Could not save local storage for current page: {e}")
            
            # Save auth tokens if any
            if 'auth_tokens' in self._session_data:
                self.credential_storage.store_auth_tokens(self.session_id, self._session_data['auth_tokens'])
            
            # Update session metadata
            self._session_data['last_saved'] = datetime.now(timezone.utc).isoformat()
            self.credential_storage.store_session_data(self.session_id, self._session_data)
            
            logger.debug("Session state saved successfully")
            
        except Exception as e:
            logger.error(f"Failed to save session state: {e}")
    
    async def _start_auto_save(self) -> None:
        """Start automatic session saving."""
        if self._auto_save_task:
            self._auto_save_task.cancel()
        
        self._auto_save_task = asyncio.create_task(self._auto_save_loop())
    
    async def _auto_save_loop(self) -> None:
        """Auto-save loop."""
        interval = self.config.session.auto_save_interval
        
        while True:
            try:
                await asyncio.sleep(interval)
                await self.save_session_state()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Auto-save failed: {e}")
    
    async def restore_local_storage_for_url(self, url: str) -> bool:
        """
        Restore local storage for a specific URL.
        
        Args:
            url: The URL to restore local storage for
            
        Returns:
            bool: True if restoration successful
        """
        try:
            local_storage_data = self.credential_storage.load_local_storage(self.session_id)
            if local_storage_data and url in local_storage_data:
                await self.cdp_handler.set_local_storage(url, local_storage_data[url])
                logger.info(f"Restored local storage for {url}")
                return True
        except Exception as e:
            logger.error(f"Failed to restore local storage for {url}: {e}")
        
        return False
    
    def store_auth_token(self, token_name: str, token_value: str) -> None:
        """
        Store an authentication token.
        
        Args:
            token_name: Name/identifier for the token
            token_value: The token value
        """
        if 'auth_tokens' not in self._session_data:
            self._session_data['auth_tokens'] = {}
        
        self._session_data['auth_tokens'][token_name] = token_value
        logger.info(f"Stored auth token: {token_name}")
    
    def get_auth_token(self, token_name: str) -> Optional[str]:
        """
        Get an authentication token.
        
        Args:
            token_name: Name/identifier for the token
            
        Returns:
            The token value if found, None otherwise
        """
        return self._session_data.get('auth_tokens', {}).get(token_name)
    
    def list_stored_sessions(self) -> List[str]:
        """
        List all stored session IDs.
        
        Returns:
            List of session IDs
        """
        return self.credential_storage.list_sessions()
    
    def delete_session(self, session_id: Optional[str] = None) -> bool:
        """
        Delete a stored session.
        
        Args:
            session_id: Session ID to delete (defaults to current session)
            
        Returns:
            True if deletion successful
        """
        target_session = session_id or self.session_id
        return self.credential_storage.delete_session(target_session)
    
    def cleanup_old_sessions(self) -> int:
        """
        Clean up old sessions based on max_session_age.
        
        Returns:
            Number of sessions deleted
        """
        return self.credential_storage.cleanup_old_sessions()
    
    @property
    def browser(self):
        """Get the browser instance."""
        return self.cdp_handler.browser
    
    @property
    def context(self):
        """Get the browser context."""
        return self.cdp_handler.context
    
    @property
    def page(self):
        """Get the current page."""
        return self.cdp_handler.page
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to browser."""
        return self.cdp_handler.is_connected
    
    async def close(self) -> None:
        """Close the session and clean up resources."""
        logger.info(f"Closing session: {self.session_id}")
        
        # Cancel auto-save
        if self._auto_save_task:
            self._auto_save_task.cancel()
        
        # Save final state
        await self.save_session_state()
        
        # Close CDP connection
        await self.cdp_handler.close()
        
        logger.info("Session closed successfully")
