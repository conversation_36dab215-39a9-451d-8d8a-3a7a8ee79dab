"""
Test script to verify the browser automation installation.
"""

import asyncio
import os
import sys
from pathlib import Path
import logging

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from browser_automation import BrowserSessionManager, AutomationEngine, Config
    from browser_automation.exceptions import BrowserAutomationError
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run 'python setup.py' first")
    sys.exit(1)


async def test_configuration():
    """Test configuration creation and validation."""
    print("🔧 Testing configuration...")
    
    try:
        # Test default configuration
        config = Config()
        print("✅ Default configuration created")
        
        # Test configuration with custom values
        config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
        config.browser.profile_name = "Profile 2"
        config.cdp.port = 9222
        
        print("✅ Configuration customization works")
        return True, config
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False, None


async def test_session_manager(config):
    """Test session manager creation and basic functionality."""
    print("\n📊 Testing session manager...")
    
    try:
        # Create session manager
        session_manager = BrowserSessionManager(config, session_id="test_session")
        print("✅ Session manager created")
        
        # Test session operations
        sessions = session_manager.list_stored_sessions()
        print(f"✅ Found {len(sessions)} stored sessions")
        
        # Test credential storage
        session_manager.store_auth_token("test_token", "test_value")
        retrieved_token = session_manager.get_auth_token("test_token")
        
        if retrieved_token == "test_value":
            print("✅ Credential storage works")
        else:
            print("❌ Credential storage failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Session manager test failed: {e}")
        return False


async def test_browser_connection(config):
    """Test browser connection (if Chrome is running)."""
    print("\n🌐 Testing browser connection...")
    
    try:
        session_manager = BrowserSessionManager(config, session_id="connection_test")
        
        # Try to connect to existing browser
        print("Attempting to connect to existing browser...")
        connected = await session_manager.connect_to_existing_browser()
        
        if connected:
            print("✅ Connected to existing browser!")
            
            # Test basic browser operations
            if session_manager.page:
                current_url = session_manager.page.url
                print(f"✅ Current URL: {current_url}")
            
            # Save and close
            await session_manager.save_session_state()
            await session_manager.close()
            print("✅ Session saved and closed")
            
            return True
        else:
            print("⚠️  Could not connect to existing browser")
            print("This is normal if Chrome is not running with debugging enabled")
            return True  # Not a failure, just no browser available
            
    except Exception as e:
        print(f"❌ Browser connection test failed: {e}")
        return False


async def test_automation_engine(config):
    """Test automation engine creation."""
    print("\n🤖 Testing automation engine...")
    
    try:
        # Check for LLM API keys
        has_api_key = any([
            os.getenv("OPENAI_API_KEY"),
            os.getenv("ANTHROPIC_API_KEY"),
            os.getenv("GEMINI_API_KEY")
        ])
        
        if not has_api_key:
            print("⚠️  No LLM API key found - skipping automation engine test")
            print("Set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GEMINI_API_KEY to test automation")
            return True
        
        session_manager = BrowserSessionManager(config, session_id="engine_test")
        
        # Try to connect to browser for engine test
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            print("⚠️  No browser connection - skipping automation engine test")
            return True
        
        # Create automation engine
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        print("✅ Automation engine initialized")
        
        # Test basic operations
        current_url = await engine.get_current_url()
        print(f"✅ Current URL retrieved: {current_url}")
        
        # Clean up
        await engine.close()
        await session_manager.close()
        print("✅ Automation engine test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Automation engine test failed: {e}")
        return False


async def test_cli_import():
    """Test CLI module import."""
    print("\n💻 Testing CLI import...")
    
    try:
        from browser_automation.cli import app
        print("✅ CLI module imported successfully")
        return True
    except Exception as e:
        print(f"❌ CLI import failed: {e}")
        return False


def test_file_structure():
    """Test that all required files are present."""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "browser_automation/__init__.py",
        "browser_automation/session_manager.py",
        "browser_automation/cdp_handler.py",
        "browser_automation/credential_storage.py",
        "browser_automation/automation_engine.py",
        "browser_automation/config.py",
        "browser_automation/exceptions.py",
        "browser_automation/cli.py",
        "examples/basic_usage.py",
        "examples/advanced_automation.py",
        "requirements.txt",
        "README.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All required files present")
        return True


async def run_comprehensive_test():
    """Run comprehensive installation test."""
    print("🧪 Browser Session Automation - Installation Test")
    print("="*60)
    
    # Test file structure
    if not test_file_structure():
        print("\n❌ File structure test failed")
        return False
    
    # Test configuration
    success, config = await test_configuration()
    if not success:
        print("\n❌ Configuration test failed")
        return False
    
    # Test session manager
    if not await test_session_manager(config):
        print("\n❌ Session manager test failed")
        return False
    
    # Test CLI import
    if not await test_cli_import():
        print("\n❌ CLI test failed")
        return False
    
    # Test browser connection (optional)
    await test_browser_connection(config)
    
    # Test automation engine (optional)
    await test_automation_engine(config)
    
    print("\n" + "="*60)
    print("🎉 Installation test completed successfully!")
    print("="*60)
    
    print("\n📋 Test Summary:")
    print("✅ File structure - OK")
    print("✅ Configuration - OK")
    print("✅ Session manager - OK")
    print("✅ CLI module - OK")
    print("✅ Browser connection - Tested (if available)")
    print("✅ Automation engine - Tested (if API key available)")
    
    print("\n🚀 Ready to use!")
    print("Run 'python examples/basic_usage.py' to get started")
    
    return True


def main():
    """Main test function."""
    try:
        # Set up basic logging
        logging.basicConfig(level=logging.WARNING)
        
        # Run comprehensive test
        success = asyncio.run(run_comprehensive_test())
        
        if not success:
            print("\n❌ Installation test failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during testing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
