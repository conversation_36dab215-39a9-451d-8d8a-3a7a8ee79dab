# Browser Session Automation

A comprehensive browser automation solution that preserves existing login sessions and provides robust session management with the browser-use library.

## 🚀 Features

- **Session Persistence**: Maintain login sessions, cookies, and authentication states across automation runs
- **Existing Browser Connection**: Connect to your existing browser instance without launching a new one
- **Secure Credential Storage**: Encrypted storage of cookies, tokens, and session data
- **Robust Error Handling**: Automatic reconnection and comprehensive error recovery
- **LLM-Powered Automation**: Natural language task execution using browser-use library
- **Multi-Browser Support**: Chrome/Chromium with existing user profiles
- **CLI Interface**: Easy-to-use command-line interface for quick automation tasks

## 📋 Requirements

- Python 3.11+
- Chrome/Chromium browser
- One of the following LLM API keys:
  - OpenAI API Key
  - Anthropic API Key  
  - Google Gemini API Key

## 🛠️ Installation

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd browser-session-automation
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers**:
   ```bash
   playwright install chromium
   ```

4. **Set up environment variables**:
   Create a `.env` file in the project root:
   ```env
   # Choose one of the following LLM providers
   OPENAI_API_KEY=your_openai_api_key_here
   # ANTHROPIC_API_KEY=your_anthropic_api_key_here
   # GEMINI_API_KEY=your_gemini_api_key_here
   ```

## 🚀 Quick Start

### Method 1: Connect to Existing Browser

1. **Launch Chrome with remote debugging**:
   ```bash
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Profile 2"
   ```

2. **Connect and start automation**:
   ```bash
   python -m browser_automation.cli connect
   ```

### Method 2: Launch New Browser

```bash
python -m browser_automation.cli launch
```

### Method 3: Execute Specific Task

```bash
python -m browser_automation.cli execute "Search for 'browser automation' on Google"
```

## 📖 Usage Examples

### Basic Python Usage

```python
import asyncio
from browser_automation import BrowserSessionManager, AutomationEngine, Config

async def main():
    # Create configuration
    config = Config()
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    # Create session manager
    session_manager = BrowserSessionManager(config, session_id="my_session")
    
    # Connect to existing browser
    await session_manager.connect_to_existing_browser()
    
    # Create automation engine
    engine = AutomationEngine(session_manager)
    await engine.initialize()
    
    # Execute automation task
    result = await engine.execute_task("Navigate to Google and search for 'AI automation'")
    
    if result['success']:
        print("✅ Task completed successfully!")
    else:
        print(f"❌ Task failed: {result['error']}")
    
    # Clean up
    await engine.close()
    await session_manager.close()

asyncio.run(main())
```

### CLI Usage

```bash
# List stored sessions
python -m browser_automation.cli sessions

# Execute a specific task
python -m browser_automation.cli execute "Fill out the contact form on example.com"

# Clean up old sessions
python -m browser_automation.cli cleanup --old

# Interactive mode
python -m browser_automation.cli connect
```

## 🔧 Configuration

### Browser Configuration

```python
from browser_automation import Config

config = Config()

# Browser settings
config.browser.executable_path = "C:/Program Files/Google/Chrome/Application/chrome.exe"
config.browser.user_data_dir = "C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
config.browser.profile_name = "Profile 2"
config.browser.headless = False
config.browser.window_size = (1920, 1080)

# CDP settings
config.cdp.host = "localhost"
config.cdp.port = 9222
config.cdp.timeout = 30
config.cdp.max_retries = 3

# Session settings
config.session.auto_save_interval = 300  # seconds
config.session.max_session_age = 86400 * 7  # 7 days
```

### Configuration File

Create `config.yaml`:

```yaml
browser:
  user_data_dir: "C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
  profile_name: "Profile 2"
  headless: false
  window_size: [1920, 1080]

cdp:
  host: "localhost"
  port: 9222
  timeout: 30
  max_retries: 3

session:
  auto_save_interval: 300
  max_session_age: 604800

logging:
  level: "INFO"
  file_path: "automation.log"
```

Load configuration:

```python
config = Config.from_file(Path("config.yaml"))
```

## 🔐 Security Features

- **Encrypted Storage**: All session data is encrypted using Fernet encryption
- **Secure Key Management**: Encryption keys are derived from user passwords using PBKDF2
- **Session Isolation**: Each session is stored separately with unique identifiers
- **Automatic Cleanup**: Old sessions are automatically cleaned up based on age

## 🛡️ Error Handling

The system includes comprehensive error handling:

- **Connection Recovery**: Automatic reconnection to browser instances
- **Session Recovery**: Restore session state after connection drops
- **Graceful Degradation**: Continue operation even if some features fail
- **Detailed Logging**: Comprehensive logging for debugging and monitoring

## 📁 Project Structure

```
browser-session-automation/
├── browser_automation/
│   ├── __init__.py
│   ├── session_manager.py      # Core session management
│   ├── cdp_handler.py          # Chrome DevTools Protocol handler
│   ├── credential_storage.py   # Encrypted credential storage
│   ├── automation_engine.py    # Browser-use integration
│   ├── config.py              # Configuration management
│   ├── exceptions.py          # Custom exceptions
│   └── cli.py                 # Command-line interface
├── examples/
│   ├── basic_usage.py         # Basic usage examples
│   └── advanced_automation.py # Advanced automation examples
├── requirements.txt
├── pyproject.toml
└── README.md
```

## 🔍 Troubleshooting

### Common Issues

1. **"No Chrome process found with remote debugging enabled"**
   - Ensure Chrome is launched with `--remote-debugging-port=9222`
   - Check that the port is not blocked by firewall

2. **"Failed to connect via CDP"**
   - Verify Chrome is running and accessible
   - Check if another application is using the debugging port

3. **"No LLM API key found"**
   - Set one of: `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, or `GEMINI_API_KEY`

4. **Session data not persisting**
   - Check file permissions in the session directory
   - Verify encryption key is accessible

### Debug Mode

Enable debug logging:

```bash
python -m browser_automation.cli connect --debug
```

Or in Python:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [browser-use](https://github.com/browser-use/browser-use) - LLM-powered browser automation
- [Playwright](https://playwright.dev/) - Browser automation framework
- [Cryptography](https://cryptography.io/) - Secure credential storage
