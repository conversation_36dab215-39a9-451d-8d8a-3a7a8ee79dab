[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "browser-session-automation"
version = "1.0.0"
description = "Browser automation with persistent session management"
authors = [{name = "AI Assistant", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "browser-use>=0.7.7",
    "playwright>=1.40.0",
    "cryptography>=41.0.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "aiofiles>=23.0.0",
    "psutil>=5.9.0",
    "rich>=13.0.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
]

[project.urls]
Homepage = "https://github.com/example/browser-session-automation"
Repository = "https://github.com/example/browser-session-automation"

[tool.setuptools.packages.find]
where = ["."]
include = ["browser_automation*"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
