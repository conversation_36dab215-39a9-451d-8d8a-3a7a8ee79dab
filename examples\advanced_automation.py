"""
Advanced automation examples with complex workflows.
"""

import asyncio
import json
import os
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from browser_automation import <PERSON>rowser<PERSON>essionManager, AutomationEngine, Config


async def social_media_automation():
    """Example of social media automation with session persistence."""
    
    config = Config()
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    session_manager = BrowserSessionManager(config, session_id="social_media_session")
    
    try:
        print("🔗 Connecting to browser for social media automation...")
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            connected = await session_manager.launch_new_browser()
        
        if not connected:
            print("❌ Failed to connect to browser")
            return
        
        print("✅ Connected!")
        
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        
        # Example: LinkedIn automation (be careful with ToS)
        print("\n🔗 Navigating to LinkedIn...")
        await engine.navigate_to_url("https://www.linkedin.com")
        
        # Check if already logged in by looking for specific elements
        print("\n🔍 Checking login status...")
        is_logged_in = await engine.wait_for_element("[data-test-id='nav-primary-navigation']", timeout=5000)
        
        if is_logged_in:
            print("✅ Already logged in to LinkedIn!")
            
            # Example: View notifications
            print("\n🔔 Checking notifications...")
            result = await engine.execute_task("Click on the notifications icon and check for new notifications")
            
            if result['success']:
                print("✅ Notifications checked successfully!")
            
        else:
            print("ℹ️  Not logged in. Please log in manually first to preserve session.")
        
        await session_manager.save_session_state()
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.close()
        await session_manager.close()


async def e_commerce_automation():
    """Example of e-commerce automation with cart management."""
    
    config = Config()
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    session_manager = BrowserSessionManager(config, session_id="ecommerce_session")
    
    try:
        print("🛒 Starting e-commerce automation...")
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            connected = await session_manager.launch_new_browser()
        
        if not connected:
            print("❌ Failed to connect to browser")
            return
        
        print("✅ Connected!")
        
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        
        # Example: Amazon product search and cart management
        print("\n🛍️ Navigating to Amazon...")
        await engine.navigate_to_url("https://www.amazon.com")
        
        print("\n🔍 Searching for a product...")
        search_task = """
        Search for 'wireless headphones' on Amazon.
        Look at the first few results and find a product with good ratings.
        Click on a product to view its details.
        """
        
        result = await engine.execute_task(search_task)
        
        if result['success']:
            print("✅ Product search completed!")
            
            # Get current page info
            current_url = await engine.get_current_url()
            page_title = await engine.get_page_title()
            
            print(f"Current page: {page_title}")
            print(f"URL: {current_url}")
            
            # Store product info in session
            session_manager.store_auth_token("last_viewed_product", current_url)
            
            print("\n📋 Getting product details...")
            product_info_task = """
            Extract the following information from this product page:
            - Product name
            - Price
            - Rating
            - Number of reviews
            - Key features
            """
            
            info_result = await engine.execute_task(product_info_task)
            
            if info_result['success']:
                print("✅ Product information extracted!")
        
        await session_manager.save_session_state()
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.close()
        await session_manager.close()


async def data_extraction_automation():
    """Example of data extraction from multiple pages."""
    
    config = Config()
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    session_manager = BrowserSessionManager(config, session_id="data_extraction_session")
    
    try:
        print("📊 Starting data extraction automation...")
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            connected = await session_manager.launch_new_browser()
        
        if not connected:
            print("❌ Failed to connect to browser")
            return
        
        print("✅ Connected!")
        
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        
        # Example: Extract data from a news website
        print("\n📰 Navigating to news website...")
        await engine.navigate_to_url("https://news.ycombinator.com")
        
        print("\n📝 Extracting article data...")
        extraction_task = """
        Extract information from the first 10 articles on this Hacker News page:
        For each article, get:
        - Title
        - Points/score
        - Number of comments
        - Author
        - Time posted
        
        Present the information in a structured format.
        """
        
        result = await engine.execute_task(extraction_task)
        
        if result['success']:
            print("✅ Data extraction completed!")
            
            # Save extracted data to session
            session_manager.store_auth_token("extracted_data_timestamp", str(asyncio.get_event_loop().time()))
            
            print("\n📸 Taking screenshot of results...")
            await engine.take_screenshot("extracted_data.png", full_page=True)
            
        else:
            print(f"❌ Data extraction failed: {result.get('error')}")
        
        await session_manager.save_session_state()
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.close()
        await session_manager.close()


async def multi_tab_automation():
    """Example of automation across multiple tabs."""
    
    config = Config()
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    session_manager = BrowserSessionManager(config, session_id="multi_tab_session")
    
    try:
        print("🗂️ Starting multi-tab automation...")
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            connected = await session_manager.launch_new_browser()
        
        if not connected:
            print("❌ Failed to connect to browser")
            return
        
        print("✅ Connected!")
        
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        
        # Open multiple tabs and perform tasks
        websites = [
            "https://www.google.com",
            "https://www.github.com",
            "https://stackoverflow.com"
        ]
        
        for i, url in enumerate(websites):
            print(f"\n🌐 Opening tab {i+1}: {url}")
            
            if i == 0:
                # First tab - just navigate
                await engine.navigate_to_url(url)
            else:
                # Open new tab
                await engine.execute_javascript("window.open('', '_blank');")
                
                # Switch to new tab
                pages = session_manager.context.pages
                if len(pages) > i:
                    await pages[i].bring_to_front()
                    await pages[i].goto(url)
            
            # Perform a task on each tab
            if "google" in url:
                task = "Search for 'browser automation python'"
            elif "github" in url:
                task = "Search for repositories related to 'browser automation'"
            elif "stackoverflow" in url:
                task = "Search for questions about 'playwright python'"
            
            print(f"🔍 Performing task: {task}")
            result = await engine.execute_task(task)
            
            if result['success']:
                print(f"✅ Task completed on tab {i+1}")
            else:
                print(f"❌ Task failed on tab {i+1}")
            
            # Take screenshot of each tab
            await engine.take_screenshot(f"tab_{i+1}_result.png")
        
        await session_manager.save_session_state()
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.close()
        await session_manager.close()


async def main():
    """Main function to run advanced examples."""
    
    print("🚀 Advanced Browser Automation Examples")
    print("=" * 50)
    
    # Check for required environment variables
    if not any([
        os.getenv("OPENAI_API_KEY"),
        os.getenv("ANTHROPIC_API_KEY"), 
        os.getenv("GEMINI_API_KEY")
    ]):
        print("⚠️  Warning: No LLM API key found!")
        print("Please set one of: OPENAI_API_KEY, ANTHROPIC_API_KEY, or GEMINI_API_KEY")
        return
    
    examples = [
        ("1", "Social Media Automation", social_media_automation),
        ("2", "E-commerce Automation", e_commerce_automation),
        ("3", "Data Extraction", data_extraction_automation),
        ("4", "Multi-tab Automation", multi_tab_automation),
    ]
    
    print("Available examples:")
    for num, name, _ in examples:
        print(f"  {num}. {name}")
    
    choice = input("\nEnter example number (1-4) or 'all' to run all: ").strip()
    
    if choice.lower() == 'all':
        for num, name, func in examples:
            print(f"\n{'='*20} {name} {'='*20}")
            await func()
            print(f"{'='*50}")
    else:
        for num, name, func in examples:
            if choice == num:
                print(f"\n{'='*20} {name} {'='*20}")
                await func()
                break
        else:
            print("Invalid choice!")


if __name__ == "__main__":
    asyncio.run(main())
