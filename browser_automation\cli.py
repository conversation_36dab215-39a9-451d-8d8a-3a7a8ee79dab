"""
Command-line interface for browser automation.
"""

import asyncio
import logging
from pathlib import Path
from typing import Optional
import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from .session_manager import BrowserSessionManager
from .automation_engine import AutomationEngine
from .config import Config
from .exceptions import BrowserAutomationError


app = typer.Typer(help="Browser Session Automation CLI")
console = Console()


@app.command()
def connect(
    session_id: Optional[str] = typer.Option(None, "--session-id", "-s", help="Session ID to use"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    debug: bool = typer.Option(False, "--debug", "-d", help="Enable debug logging")
):
    """Connect to existing browser and start interactive session."""
    asyncio.run(_connect_command(session_id, config_file, debug))


@app.command()
def launch(
    session_id: Optional[str] = typer.Option(None, "--session-id", "-s", help="Session ID to use"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    debug: bool = typer.Option(False, "--debug", "-d", help="Enable debug logging")
):
    """Launch new browser with debugging enabled."""
    asyncio.run(_launch_command(session_id, config_file, debug))


@app.command()
def execute(
    task: str = typer.Argument(..., help="Task description to execute"),
    session_id: Optional[str] = typer.Option(None, "--session-id", "-s", help="Session ID to use"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    max_steps: int = typer.Option(50, "--max-steps", "-m", help="Maximum steps to execute"),
    debug: bool = typer.Option(False, "--debug", "-d", help="Enable debug logging")
):
    """Execute a specific automation task."""
    asyncio.run(_execute_command(task, session_id, config_file, max_steps, debug))


@app.command()
def sessions():
    """List all stored sessions."""
    _list_sessions_command()


@app.command()
def cleanup(
    session_id: Optional[str] = typer.Option(None, "--session-id", "-s", help="Specific session to delete"),
    old: bool = typer.Option(False, "--old", help="Delete old sessions based on max_session_age"),
    confirm: bool = typer.Option(False, "--yes", "-y", help="Skip confirmation prompt")
):
    """Clean up stored sessions."""
    _cleanup_command(session_id, old, confirm)


async def _connect_command(session_id: Optional[str], config_file: Optional[Path], debug: bool):
    """Connect to existing browser command implementation."""
    try:
        # Setup logging
        if debug:
            logging.basicConfig(level=logging.DEBUG)
        
        # Load configuration
        config = Config()
        if config_file:
            config = Config.from_file(config_file)
        
        # Create session manager
        session_manager = BrowserSessionManager(config, session_id)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Connecting to browser...", total=None)
            
            # Connect to existing browser
            success = await session_manager.connect_to_existing_browser()
            
            if success:
                progress.update(task, description="Connected successfully!")
                console.print(Panel(
                    f"✅ Connected to browser\n"
                    f"Session ID: {session_manager.session_id}\n"
                    f"Browser connected: {session_manager.is_connected}",
                    title="Connection Status",
                    style="green"
                ))
                
                # Start interactive mode
                await _interactive_mode(session_manager)
            else:
                console.print(Panel(
                    "❌ Failed to connect to existing browser\n"
                    "Make sure Chrome is running with --remote-debugging-port=9222",
                    title="Connection Failed",
                    style="red"
                ))
                
    except Exception as e:
        console.print(Panel(f"❌ Error: {e}", title="Error", style="red"))


async def _launch_command(session_id: Optional[str], config_file: Optional[Path], debug: bool):
    """Launch new browser command implementation."""
    try:
        # Setup logging
        if debug:
            logging.basicConfig(level=logging.DEBUG)
        
        # Load configuration
        config = Config()
        if config_file:
            config = Config.from_file(config_file)
        
        # Create session manager
        session_manager = BrowserSessionManager(config, session_id)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Launching browser...", total=None)
            
            # Launch new browser
            success = await session_manager.launch_new_browser()
            
            if success:
                progress.update(task, description="Browser launched successfully!")
                console.print(Panel(
                    f"✅ Browser launched with debugging\n"
                    f"Session ID: {session_manager.session_id}\n"
                    f"Browser connected: {session_manager.is_connected}",
                    title="Launch Status",
                    style="green"
                ))
                
                # Start interactive mode
                await _interactive_mode(session_manager)
            else:
                console.print(Panel(
                    "❌ Failed to launch browser",
                    title="Launch Failed",
                    style="red"
                ))
                
    except Exception as e:
        console.print(Panel(f"❌ Error: {e}", title="Error", style="red"))


async def _execute_command(
    task: str, 
    session_id: Optional[str], 
    config_file: Optional[Path], 
    max_steps: int, 
    debug: bool
):
    """Execute task command implementation."""
    try:
        # Setup logging
        if debug:
            logging.basicConfig(level=logging.DEBUG)
        
        # Load configuration
        config = Config()
        if config_file:
            config = Config.from_file(config_file)
        
        # Create session manager
        session_manager = BrowserSessionManager(config, session_id)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            connect_task = progress.add_task("Connecting to browser...", total=None)
            
            # Try to connect to existing browser first
            success = await session_manager.connect_to_existing_browser()
            if not success:
                progress.update(connect_task, description="Launching new browser...")
                success = await session_manager.launch_new_browser()
            
            if not success:
                console.print(Panel("❌ Failed to connect or launch browser", title="Error", style="red"))
                return
            
            progress.update(connect_task, description="Connected!")
            
            # Create automation engine
            engine = AutomationEngine(session_manager)
            await engine.initialize()
            
            exec_task = progress.add_task(f"Executing: {task[:50]}...", total=None)
            
            # Execute the task
            result = await engine.execute_task(task, max_steps)
            
            progress.update(exec_task, description="Task completed!")
            
            # Display results
            if result['success']:
                console.print(Panel(
                    f"✅ Task completed successfully\n"
                    f"Final URL: {result.get('final_url', 'N/A')}\n"
                    f"Session ID: {result['session_id']}",
                    title="Task Result",
                    style="green"
                ))
            else:
                console.print(Panel(
                    f"❌ Task failed\n"
                    f"Error: {result.get('error', 'Unknown error')}\n"
                    f"Session ID: {result['session_id']}",
                    title="Task Result",
                    style="red"
                ))
            
            # Close resources
            await engine.close()
            await session_manager.close()
            
    except Exception as e:
        console.print(Panel(f"❌ Error: {e}", title="Error", style="red"))


def _list_sessions_command():
    """List sessions command implementation."""
    try:
        config = Config()
        session_manager = BrowserSessionManager(config)
        sessions = session_manager.list_stored_sessions()
        
        if not sessions:
            console.print("No stored sessions found.")
            return
        
        table = Table(title="Stored Sessions")
        table.add_column("Session ID", style="cyan")
        table.add_column("Status", style="green")
        
        for session_id in sessions:
            table.add_row(session_id, "Stored")
        
        console.print(table)
        
    except Exception as e:
        console.print(Panel(f"❌ Error: {e}", title="Error", style="red"))


def _cleanup_command(session_id: Optional[str], old: bool, confirm: bool):
    """Cleanup command implementation."""
    try:
        config = Config()
        session_manager = BrowserSessionManager(config)
        
        if session_id:
            # Delete specific session
            if not confirm:
                if not typer.confirm(f"Delete session {session_id}?"):
                    console.print("Cancelled.")
                    return
            
            success = session_manager.delete_session(session_id)
            if success:
                console.print(f"✅ Deleted session: {session_id}")
            else:
                console.print(f"❌ Session not found: {session_id}")
                
        elif old:
            # Delete old sessions
            if not confirm:
                if not typer.confirm("Delete old sessions based on max_session_age?"):
                    console.print("Cancelled.")
                    return
            
            deleted_count = session_manager.cleanup_old_sessions()
            console.print(f"✅ Deleted {deleted_count} old sessions")
            
        else:
            console.print("Please specify --session-id or --old")
            
    except Exception as e:
        console.print(Panel(f"❌ Error: {e}", title="Error", style="red"))


async def _interactive_mode(session_manager: BrowserSessionManager):
    """Interactive mode for manual control."""
    console.print(Panel(
        "🚀 Interactive Mode\n"
        "Commands:\n"
        "  task <description> - Execute automation task\n"
        "  nav <url> - Navigate to URL\n"
        "  screenshot [path] - Take screenshot\n"
        "  js <script> - Execute JavaScript\n"
        "  save - Save session state\n"
        "  quit - Exit interactive mode",
        title="Interactive Mode",
        style="blue"
    ))
    
    engine = AutomationEngine(session_manager)
    await engine.initialize()
    
    try:
        while True:
            command = console.input("\n[bold blue]automation>[/bold blue] ")
            
            if command.lower() in ['quit', 'exit', 'q']:
                break
            elif command.startswith('task '):
                task_desc = command[5:]
                console.print(f"Executing task: {task_desc}")
                result = await engine.execute_task(task_desc)
                if result['success']:
                    console.print("✅ Task completed")
                else:
                    console.print(f"❌ Task failed: {result.get('error')}")
            elif command.startswith('nav '):
                url = command[4:]
                success = await engine.navigate_to_url(url)
                if success:
                    console.print(f"✅ Navigated to: {url}")
                else:
                    console.print(f"❌ Navigation failed")
            elif command.startswith('screenshot'):
                parts = command.split()
                path = parts[1] if len(parts) > 1 else None
                await engine.take_screenshot(path)
                console.print(f"✅ Screenshot taken{' and saved to ' + path if path else ''}")
            elif command.startswith('js '):
                script = command[3:]
                try:
                    result = await engine.execute_javascript(script)
                    console.print(f"Result: {result}")
                except Exception as e:
                    console.print(f"❌ JavaScript error: {e}")
            elif command == 'save':
                await session_manager.save_session_state()
                console.print("✅ Session state saved")
            elif command.strip() == '':
                continue
            else:
                console.print("Unknown command. Type 'quit' to exit.")
                
    except KeyboardInterrupt:
        console.print("\nExiting...")
    finally:
        await engine.close()
        await session_manager.close()


if __name__ == "__main__":
    app()
