"""
Secure credential storage for browser automation.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import getpass
from datetime import datetime, timezone

from .exceptions import CredentialError
from .config import Config


class CredentialStorage:
    """Secure storage for browser credentials, cookies, and session data."""
    
    def __init__(self, config: Config):
        self.config = config
        self.session_dir = config.session.session_dir
        self.session_dir.mkdir(parents=True, exist_ok=True)
        
        self._encryption_key: Optional[bytes] = None
        self._fernet: Optional[Fernet] = None
        
    def _get_encryption_key(self) -> bytes:
        """Get or create encryption key."""
        if self._encryption_key is not None:
            return self._encryption_key
            
        key_file = self.config.session.encryption_key_file
        if key_file is None:
            key_file = self.session_dir / "encryption.key"
            
        if key_file.exists():
            # Load existing key
            try:
                with open(key_file, 'rb') as f:
                    self._encryption_key = f.read()
            except Exception as e:
                raise CredentialError(f"Failed to load encryption key: {e}")
        else:
            # Generate new key
            password = getpass.getpass("Enter password for credential encryption: ").encode()
            salt = os.urandom(16)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            
            # Save key and salt
            key_data = {
                'key': base64.b64encode(key).decode(),
                'salt': base64.b64encode(salt).decode(),
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            
            try:
                with open(key_file, 'w') as f:
                    json.dump(key_data, f)
                os.chmod(key_file, 0o600)  # Restrict permissions
                self._encryption_key = key
            except Exception as e:
                raise CredentialError(f"Failed to save encryption key: {e}")
                
        return self._encryption_key
    
    def _get_fernet(self) -> Fernet:
        """Get Fernet encryption instance."""
        if self._fernet is None:
            key = self._get_encryption_key()
            self._fernet = Fernet(key)
        return self._fernet
    
    def store_session_data(self, session_id: str, data: Dict[str, Any]) -> None:
        """Store encrypted session data."""
        try:
            fernet = self._get_fernet()
            
            # Add metadata
            session_data = {
                'data': data,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat(),
                'session_id': session_id
            }
            
            # Encrypt and save
            json_data = json.dumps(session_data).encode()
            encrypted_data = fernet.encrypt(json_data)
            
            session_file = self.session_dir / f"{session_id}.session"
            with open(session_file, 'wb') as f:
                f.write(encrypted_data)
                
        except Exception as e:
            raise CredentialError(f"Failed to store session data: {e}")
    
    def load_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Load and decrypt session data."""
        session_file = self.session_dir / f"{session_id}.session"
        
        if not session_file.exists():
            return None
            
        try:
            fernet = self._get_fernet()
            
            with open(session_file, 'rb') as f:
                encrypted_data = f.read()
                
            decrypted_data = fernet.decrypt(encrypted_data)
            session_data = json.loads(decrypted_data.decode())
            
            # Update access time
            session_data['last_accessed'] = datetime.now(timezone.utc).isoformat()
            self.store_session_data(session_id, session_data['data'])
            
            return session_data['data']
            
        except Exception as e:
            raise CredentialError(f"Failed to load session data: {e}")
    
    def store_cookies(self, session_id: str, cookies: List[Dict[str, Any]]) -> None:
        """Store browser cookies."""
        cookie_data = {
            'cookies': cookies,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        current_data = self.load_session_data(session_id) or {}
        current_data['cookies'] = cookie_data
        self.store_session_data(session_id, current_data)
    
    def load_cookies(self, session_id: str) -> Optional[List[Dict[str, Any]]]:
        """Load browser cookies."""
        session_data = self.load_session_data(session_id)
        if session_data and 'cookies' in session_data:
            return session_data['cookies']['cookies']
        return None
    
    def store_local_storage(self, session_id: str, local_storage: Dict[str, str]) -> None:
        """Store browser local storage data."""
        storage_data = {
            'local_storage': local_storage,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        current_data = self.load_session_data(session_id) or {}
        current_data['local_storage'] = storage_data
        self.store_session_data(session_id, current_data)
    
    def load_local_storage(self, session_id: str) -> Optional[Dict[str, str]]:
        """Load browser local storage data."""
        session_data = self.load_session_data(session_id)
        if session_data and 'local_storage' in session_data:
            return session_data['local_storage']['local_storage']
        return None
    
    def store_auth_tokens(self, session_id: str, tokens: Dict[str, str]) -> None:
        """Store authentication tokens."""
        token_data = {
            'tokens': tokens,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        current_data = self.load_session_data(session_id) or {}
        current_data['auth_tokens'] = token_data
        self.store_session_data(session_id, current_data)
    
    def load_auth_tokens(self, session_id: str) -> Optional[Dict[str, str]]:
        """Load authentication tokens."""
        session_data = self.load_session_data(session_id)
        if session_data and 'auth_tokens' in session_data:
            return session_data['auth_tokens']['tokens']
        return None
    
    def list_sessions(self) -> List[str]:
        """List all stored session IDs."""
        session_files = self.session_dir.glob("*.session")
        return [f.stem for f in session_files]
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a stored session."""
        session_file = self.session_dir / f"{session_id}.session"
        if session_file.exists():
            try:
                session_file.unlink()
                return True
            except Exception as e:
                raise CredentialError(f"Failed to delete session: {e}")
        return False
    
    def cleanup_old_sessions(self) -> int:
        """Remove sessions older than max_session_age."""
        max_age = self.config.session.max_session_age
        current_time = datetime.now(timezone.utc)
        deleted_count = 0
        
        for session_file in self.session_dir.glob("*.session"):
            try:
                # Check file modification time
                mtime = datetime.fromtimestamp(session_file.stat().st_mtime, timezone.utc)
                age = (current_time - mtime).total_seconds()
                
                if age > max_age:
                    session_file.unlink()
                    deleted_count += 1
                    
            except Exception:
                # Skip files that can't be processed
                continue
                
        return deleted_count
