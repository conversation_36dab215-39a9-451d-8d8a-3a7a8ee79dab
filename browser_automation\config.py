"""
Configuration management for browser automation.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class BrowserConfig(BaseModel):
    """Browser-specific configuration."""
    
    executable_path: Optional[str] = None
    user_data_dir: Optional[str] = None
    profile_name: str = "Default"
    headless: bool = False
    window_size: tuple[int, int] = (1920, 1080)
    disable_extensions: bool = False
    disable_web_security: bool = False
    args: List[str] = Field(default_factory=list)


class CDPConfig(BaseModel):
    """Chrome DevTools Protocol configuration."""
    
    host: str = "localhost"
    port: int = 9222
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    heartbeat_interval: float = 30.0


class SessionConfig(BaseModel):
    """Session management configuration."""
    
    session_dir: Path = Field(default_factory=lambda: Path.home() / ".browser_automation" / "sessions")
    encryption_key_file: Optional[Path] = None
    auto_save_interval: int = 300  # seconds
    max_session_age: int = 86400 * 7  # 7 days in seconds
    backup_count: int = 5


class LoggingConfig(BaseModel):
    """Logging configuration."""
    
    level: str = "INFO"
    file_path: Optional[Path] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


class Config(BaseModel):
    """Main configuration class."""
    
    browser: BrowserConfig = Field(default_factory=BrowserConfig)
    cdp: CDPConfig = Field(default_factory=CDPConfig)
    session: SessionConfig = Field(default_factory=SessionConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # LLM Configuration
    openai_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))
    anthropic_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("ANTHROPIC_API_KEY"))
    gemini_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("GEMINI_API_KEY"))
    
    @validator('session')
    def create_session_dir(cls, v):
        """Ensure session directory exists."""
        v.session_dir.mkdir(parents=True, exist_ok=True)
        return v
    
    @classmethod
    def from_file(cls, config_path: Path) -> "Config":
        """Load configuration from file."""
        if config_path.suffix.lower() == '.json':
            import json
            with open(config_path, 'r') as f:
                data = json.load(f)
        elif config_path.suffix.lower() in ['.yml', '.yaml']:
            import yaml
            with open(config_path, 'r') as f:
                data = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported config file format: {config_path.suffix}")
        
        return cls(**data)
    
    def save_to_file(self, config_path: Path) -> None:
        """Save configuration to file."""
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        if config_path.suffix.lower() == '.json':
            import json
            with open(config_path, 'w') as f:
                json.dump(self.dict(), f, indent=2, default=str)
        elif config_path.suffix.lower() in ['.yml', '.yaml']:
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(self.dict(), f, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported config file format: {config_path.suffix}")


# Default configuration instance
default_config = Config()
