# Example configuration for browser automation
browser:
  # Update these paths to match your system
  user_data_dir: "C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
  profile_name: "Profile 2"
  headless: false
  window_size: [1920, 1080]
  args:
    - "--disable-web-security"
    - "--disable-features=VizDisplayCompositor"

cdp:
  host: "localhost"
  port: 9222
  timeout: 30
  max_retries: 3
  retry_delay: 1.0
  heartbeat_interval: 30.0

session:
  auto_save_interval: 300  # seconds
  max_session_age: 604800  # 7 days
  backup_count: 5

logging:
  level: "INFO"
  file_path: "automation.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
