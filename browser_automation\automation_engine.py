"""
Browser automation engine integrating with browser-use library.
"""

import asyncio
import logging
from typing import Optional, Dict, Any, Union
from browser_use import Agent, Browser, ChatOpenAI, ChatAnthropic, ChatGoogle

from .session_manager import BrowserSessionManager
from .config import Config
from .exceptions import TaskExecutionError, ConfigurationError


logger = logging.getLogger(__name__)


class AutomationEngine:
    """
    Browser automation engine that integrates browser-use with session management.
    """
    
    def __init__(self, session_manager: BrowserSessionManager, config: Optional[Config] = None):
        self.session_manager = session_manager
        self.config = config or session_manager.config
        
        self._browser_use: Optional[Browser] = None
        self._llm = None
        
    async def initialize(self) -> None:
        """Initialize the automation engine."""
        try:
            logger.info("Initializing automation engine")
            
            # Ensure session manager is connected
            if not self.session_manager.is_connected:
                raise TaskExecutionError("Session manager is not connected to browser")
            
            # Initialize LLM
            self._llm = self._create_llm()
            
            # Create browser-use Browser instance using CDP connection
            if self.session_manager.is_connected:
                # Use CDP URL to connect browser-use to the existing browser
                cdp_url = f"http://{self.config.cdp.host}:{self.config.cdp.port}"
                self._browser_use = Browser(cdp_url=cdp_url)
                logger.info("Browser-use integration initialized with CDP connection")
            else:
                raise TaskExecutionError("No browser connection available")
                
        except Exception as e:
            logger.error(f"Failed to initialize automation engine: {e}")
            raise TaskExecutionError(f"Initialization failed: {e}")
    
    def _create_llm(self):
        """Create LLM instance based on available API keys."""
        # Try OpenAI first
        if self.config.openai_api_key:
            logger.info("Using OpenAI LLM")
            return ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0.1
            )

        # Try Anthropic
        elif self.config.anthropic_api_key:
            logger.info("Using Anthropic LLM")
            return ChatAnthropic(
                model="claude-3-5-sonnet-20241022",
                temperature=0.1
            )

        # Try Google Gemini
        elif self.config.gemini_api_key:
            logger.info("Using Google Gemini LLM")
            return ChatGoogle(
                model="gemini-1.5-flash",
                temperature=0.1
            )

        else:
            raise ConfigurationError(
                "No LLM API key found. Please set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GEMINI_API_KEY"
            )
    
    async def execute_task(self, task: str, max_steps: int = 50) -> Dict[str, Any]:
        """
        Execute an automation task using browser-use.
        
        Args:
            task: Natural language description of the task
            max_steps: Maximum number of steps to execute
            
        Returns:
            Dictionary containing execution results
        """
        try:
            logger.info(f"Executing task: {task}")
            
            if not self._browser_use or not self._llm:
                await self.initialize()
            
            # Save session state before task execution
            await self.session_manager.save_session_state()
            
            # Create browser-use agent
            agent = Agent(
                task=task,
                llm=self._llm,
                browser=self._browser_use,
                max_steps=max_steps
            )
            
            # Execute the task
            result = await agent.run()
            
            # Save session state after task execution
            await self.session_manager.save_session_state()
            
            logger.info("Task execution completed successfully")
            
            return {
                'success': True,
                'task': task,
                'result': result,
                'session_id': self.session_manager.session_id,
                'steps_taken': getattr(result, 'steps_taken', None),
                'final_url': self.session_manager.page.url if self.session_manager.page else None
            }
            
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            
            # Try to save session state even if task failed
            try:
                await self.session_manager.save_session_state()
            except:
                pass
            
            return {
                'success': False,
                'task': task,
                'error': str(e),
                'session_id': self.session_manager.session_id
            }
    
    async def navigate_to_url(self, url: str, restore_local_storage: bool = True) -> bool:
        """
        Navigate to a URL and optionally restore local storage.
        
        Args:
            url: URL to navigate to
            restore_local_storage: Whether to restore local storage for this URL
            
        Returns:
            True if navigation successful
        """
        try:
            if not self.session_manager.page:
                raise TaskExecutionError("No page available for navigation")
            
            logger.info(f"Navigating to: {url}")
            
            # Navigate to URL
            await self.session_manager.page.goto(url, wait_until='networkidle')
            
            # Restore local storage if requested
            if restore_local_storage:
                await self.session_manager.restore_local_storage_for_url(url)
            
            # Save session state after navigation
            await self.session_manager.save_session_state()
            
            logger.info(f"Successfully navigated to: {url}")
            return True
            
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return False
    
    async def execute_javascript(self, script: str) -> Any:
        """
        Execute JavaScript in the current page.
        
        Args:
            script: JavaScript code to execute
            
        Returns:
            Result of the JavaScript execution
        """
        try:
            if not self.session_manager.page:
                raise TaskExecutionError("No page available for JavaScript execution")
            
            logger.debug(f"Executing JavaScript: {script[:100]}...")
            result = await self.session_manager.page.evaluate(script)
            
            return result
            
        except Exception as e:
            logger.error(f"JavaScript execution failed: {e}")
            raise TaskExecutionError(f"JavaScript execution failed: {e}")
    
    async def take_screenshot(self, path: Optional[str] = None, full_page: bool = False) -> bytes:
        """
        Take a screenshot of the current page.
        
        Args:
            path: Optional path to save screenshot
            full_page: Whether to capture full page
            
        Returns:
            Screenshot as bytes
        """
        try:
            if not self.session_manager.page:
                raise TaskExecutionError("No page available for screenshot")
            
            screenshot = await self.session_manager.page.screenshot(
                path=path,
                full_page=full_page
            )
            
            logger.info(f"Screenshot taken{' and saved to ' + path if path else ''}")
            return screenshot
            
        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            raise TaskExecutionError(f"Screenshot failed: {e}")
    
    async def wait_for_element(self, selector: str, timeout: int = 30000) -> bool:
        """
        Wait for an element to appear on the page.
        
        Args:
            selector: CSS selector for the element
            timeout: Timeout in milliseconds
            
        Returns:
            True if element appeared, False if timeout
        """
        try:
            if not self.session_manager.page:
                raise TaskExecutionError("No page available")
            
            await self.session_manager.page.wait_for_selector(selector, timeout=timeout)
            return True
            
        except Exception as e:
            logger.warning(f"Element wait timeout or failed: {e}")
            return False
    
    async def get_page_content(self) -> str:
        """
        Get the current page's HTML content.
        
        Returns:
            HTML content as string
        """
        try:
            if not self.session_manager.page:
                raise TaskExecutionError("No page available")
            
            content = await self.session_manager.page.content()
            return content
            
        except Exception as e:
            logger.error(f"Failed to get page content: {e}")
            raise TaskExecutionError(f"Failed to get page content: {e}")
    
    async def get_page_title(self) -> str:
        """
        Get the current page's title.
        
        Returns:
            Page title
        """
        try:
            if not self.session_manager.page:
                raise TaskExecutionError("No page available")
            
            title = await self.session_manager.page.title()
            return title
            
        except Exception as e:
            logger.error(f"Failed to get page title: {e}")
            raise TaskExecutionError(f"Failed to get page title: {e}")
    
    async def get_current_url(self) -> str:
        """
        Get the current page's URL.
        
        Returns:
            Current URL
        """
        try:
            if not self.session_manager.page:
                raise TaskExecutionError("No page available")
            
            return self.session_manager.page.url
            
        except Exception as e:
            logger.error(f"Failed to get current URL: {e}")
            raise TaskExecutionError(f"Failed to get current URL: {e}")
    
    async def close(self) -> None:
        """Close the automation engine and clean up resources."""
        logger.info("Closing automation engine")
        
        if self._browser_use:
            try:
                await self._browser_use.close()
            except:
                pass
            self._browser_use = None
        
        self._llm = None
        
        logger.info("Automation engine closed")
