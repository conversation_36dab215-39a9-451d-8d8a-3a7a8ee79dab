"""
Browser Session Automation

A comprehensive browser automation solution that preserves existing login sessions
and provides robust session management with the browser-use library.
"""

from .session_manager import BrowserSessionManager
from .automation_engine import AutomationEngine
from .config import Config
from .exceptions import (
    BrowserAutomationError,
    SessionError,
    ConnectionError,
    CredentialError,
)

__version__ = "1.0.0"
__all__ = [
    "BrowserSessionManager",
    "AutomationEngine", 
    "Config",
    "BrowserAutomationError",
    "SessionError",
    "ConnectionError",
    "CredentialError",
]
