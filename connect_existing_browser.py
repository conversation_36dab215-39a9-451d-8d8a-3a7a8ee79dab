#!/usr/bin/env python3
"""
Scrip<PERSON> to connect to your existing Chrome browser with all login sessions preserved.
"""

import asyncio
import subprocess
import time
import requests
import os
import sys
from pathlib import Path

def check_chrome_debugging():
    """Check if Chrome is running with debugging enabled."""
    try:
        response = requests.get("http://localhost:9222/json/version", timeout=2)
        return response.status_code == 200
    except:
        return False

def get_chrome_processes():
    """Get running Chrome processes."""
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq chrome.exe'], 
                              capture_output=True, text=True)
        return 'chrome.exe' in result.stdout
    except:
        return False

def start_chrome_with_debugging():
    """Start Chrome with debugging enabled."""
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
    ]
    
    chrome_exe = None
    for path in chrome_paths:
        if os.path.exists(path):
            chrome_exe = path
            break
    
    if not chrome_exe:
        print("❌ Chrome not found in standard locations")
        return False
    
    # Your profile path from earlier
    user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    profile_dir = "Profile 2"
    
    cmd = [
        chrome_exe,
        "--remote-debugging-port=9222",
        f"--user-data-dir={user_data_dir}",
        f"--profile-directory={profile_dir}",
        "--no-first-run",
        "--no-default-browser-check"
    ]
    
    print(f"🚀 Starting Chrome with debugging...")
    print(f"   Profile: {profile_dir}")
    print(f"   User Data: {user_data_dir}")
    
    try:
        subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
        return True
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        return False

async def test_automation():
    """Test the automation with your real browser."""
    from browser_automation import BrowserSessionManager, AutomationEngine, Config
    
    print("\n🤖 Testing automation with your real browser...")
    
    # Create config
    config = Config()
    
    # Create session manager
    session_manager = BrowserSessionManager(config, "real_browser_test")
    
    try:
        # Connect to existing browser
        print("🔗 Connecting to your browser...")
        success = await session_manager.connect_to_existing_browser()
        
        if not success:
            print("❌ Failed to connect to browser")
            return
        
        print("✅ Connected to your real browser!")
        
        # Get current page info
        current_url = await session_manager.get_current_url()
        print(f"📍 Current page: {current_url}")
        
        # Initialize automation engine
        automation = AutomationEngine(session_manager, config)
        
        print("\n🧭 Testing navigation...")
        await automation.navigate("https://httpbin.org")
        print("✅ Navigation successful!")
        
        print("\n📸 Taking screenshot...")
        screenshot_path = await automation.take_screenshot("real_browser_test.png")
        print(f"✅ Screenshot saved: {screenshot_path}")
        
        print("\n🎯 Testing simple automation task...")
        result = await automation.execute_task(
            "Look at this page and tell me what you see. Just describe the main heading and purpose of the site."
        )
        print(f"🤖 AI Response: {result}")
        
        print("\n✅ All tests passed! Your real browser is working with automation!")
        
    except Exception as e:
        print(f"❌ Error during automation test: {e}")
    finally:
        await session_manager.close()

def main():
    print("🌐 Real Browser Connection Tool")
    print("=" * 50)
    
    # Check if Chrome is already running with debugging
    if check_chrome_debugging():
        print("✅ Chrome is already running with debugging enabled!")
    else:
        print("🔍 Chrome debugging not detected...")
        
        # Check if Chrome is running at all
        if get_chrome_processes():
            print("⚠️  Chrome is running but without debugging.")
            print("   Please close Chrome completely and run this script again.")
            print("   Or manually restart Chrome with: --remote-debugging-port=9222")
            return
        
        # Start Chrome with debugging
        if not start_chrome_with_debugging():
            return
        
        # Wait for Chrome to start
        print("⏳ Waiting for Chrome to start...")
        for i in range(10):
            time.sleep(1)
            if check_chrome_debugging():
                print("✅ Chrome started with debugging enabled!")
                break
            print(f"   Waiting... ({i+1}/10)")
        else:
            print("❌ Chrome failed to start with debugging after 10 seconds")
            return
    
    # Test the connection
    print("\n🧪 Testing browser connection...")
    try:
        response = requests.get("http://localhost:9222/json", timeout=5)
        tabs = response.json()
        print(f"✅ Found {len(tabs)} browser tabs/windows")
        
        # Show current tabs
        print("\n📑 Current browser tabs:")
        for i, tab in enumerate(tabs[:5]):  # Show first 5 tabs
            title = tab.get('title', 'No title')[:50]
            url = tab.get('url', 'No URL')[:60]
            print(f"   {i+1}. {title} - {url}")
        
        if len(tabs) > 5:
            print(f"   ... and {len(tabs) - 5} more tabs")
            
    except Exception as e:
        print(f"❌ Failed to connect to browser: {e}")
        return
    
    # Run automation test
    print("\n" + "=" * 50)
    print("🚀 Running automation test...")
    asyncio.run(test_automation())

if __name__ == "__main__":
    main()
