"""
Custom exceptions for browser automation.
"""


class BrowserAutomationError(Exception):
    """Base exception for browser automation errors."""
    pass


class SessionError(BrowserAutomationError):
    """Raised when session management operations fail."""
    pass


class ConnectionError(BrowserAutomationError):
    """Raised when CDP connection operations fail."""
    pass


class CredentialError(BrowserAutomationError):
    """Raised when credential storage/retrieval operations fail."""
    pass


class ConfigurationError(BrowserAutomationError):
    """Raised when configuration is invalid."""
    pass


class BrowserNotFoundError(BrowserAutomationError):
    """Raised when the target browser instance cannot be found."""
    pass


class TaskExecutionError(BrowserAutomationError):
    """Raised when automation task execution fails."""
    pass
