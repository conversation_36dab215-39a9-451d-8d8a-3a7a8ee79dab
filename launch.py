"""
Easy launcher script for browser session automation.
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path


def print_banner():
    """Print application banner."""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                Browser Session Automation                    ║
║              Preserve Sessions • Automate Tasks             ║
╚══════════════════════════════════════════════════════════════╝
    """)


def check_installation():
    """Check if the application is properly installed."""
    required_files = [
        "browser_automation/__init__.py",
        "requirements.txt",
        "README.md"
    ]
    
    missing = [f for f in required_files if not Path(f).exists()]
    
    if missing:
        print(f"❌ Missing files: {missing}")
        print("Please ensure all files are present.")
        return False
    
    return True


def check_dependencies():
    """Check if dependencies are installed."""
    try:
        import browser_automation
        return True
    except ImportError:
        return False


def install_dependencies():
    """Install dependencies."""
    print("📦 Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False


def check_api_keys():
    """Check for LLM API keys."""
    api_keys = {
        'OpenAI': os.getenv('OPENAI_API_KEY'),
        'Anthropic': os.getenv('ANTHROPIC_API_KEY'),
        'Gemini': os.getenv('GEMINI_API_KEY')
    }
    
    available = [name for name, key in api_keys.items() if key]
    
    if available:
        print(f"✅ LLM API keys found: {', '.join(available)}")
        return True
    else:
        print("⚠️  No LLM API keys found")
        print("Set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GEMINI_API_KEY for full functionality")
        return False


def launch_chrome_with_debugging():
    """Launch Chrome with remote debugging enabled."""
    chrome_paths = [
        "C:/Program Files/Google/Chrome/Application/chrome.exe",
        "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
    ]
    
    chrome_path = None
    for path in chrome_paths:
        if Path(path).exists():
            chrome_path = path
            break
    
    if not chrome_path:
        print("❌ Chrome not found in standard locations")
        print("Please launch Chrome manually with: --remote-debugging-port=9222")
        return False
    
    user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    profile = "Profile 2"
    
    cmd = [
        chrome_path,
        "--remote-debugging-port=9222",
        f"--user-data-dir={user_data_dir}",
        f"--profile-directory={profile}"
    ]
    
    try:
        print("🚀 Launching Chrome with debugging...")
        subprocess.Popen(cmd)
        print("✅ Chrome launched with remote debugging on port 9222")
        return True
    except Exception as e:
        print(f"❌ Failed to launch Chrome: {e}")
        return False


def show_menu():
    """Show main menu."""
    print("\n📋 What would you like to do?")
    print("1. 🔧 Setup and install dependencies")
    print("2. 🌐 Launch Chrome with debugging")
    print("3. 🔗 Connect to existing browser")
    print("4. 🚀 Launch new browser session")
    print("5. 🧪 Run installation test")
    print("6. 📖 Run basic examples")
    print("7. 🎯 Run complete workflow demo")
    print("8. 💻 Start CLI interactive mode")
    print("9. ❓ Show help and documentation")
    print("0. 🚪 Exit")


def run_setup():
    """Run setup process."""
    print("\n🔧 Running setup...")
    try:
        subprocess.run([sys.executable, "setup.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Setup failed")
        return False


def run_test():
    """Run installation test."""
    print("\n🧪 Running installation test...")
    try:
        subprocess.run([sys.executable, "test_installation.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Test failed")
        return False


def run_examples():
    """Run basic examples."""
    print("\n📖 Running basic examples...")
    try:
        subprocess.run([sys.executable, "examples/basic_usage.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Examples failed")
        return False


def run_workflow_demo():
    """Run complete workflow demo."""
    print("\n🎯 Running complete workflow demo...")
    try:
        subprocess.run([sys.executable, "examples/complete_workflow.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Workflow demo failed")
        return False


def run_cli_connect():
    """Run CLI connect command."""
    print("\n🔗 Connecting to existing browser...")
    try:
        subprocess.run([sys.executable, "-m", "browser_automation.cli", "connect"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ CLI connect failed")
        return False


def run_cli_launch():
    """Run CLI launch command."""
    print("\n🚀 Launching new browser session...")
    try:
        subprocess.run([sys.executable, "-m", "browser_automation.cli", "launch"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ CLI launch failed")
        return False


def show_help():
    """Show help information."""
    print("\n❓ Help and Documentation")
    print("="*50)
    print("📚 Documentation files:")
    print("   - README.md - Complete documentation")
    print("   - examples/ - Usage examples")
    print("   - config.example.yaml - Configuration reference")
    
    print("\n🔧 Setup steps:")
    print("   1. Run option 1 to install dependencies")
    print("   2. Set LLM API key in .env file")
    print("   3. Launch Chrome with debugging (option 2)")
    print("   4. Connect to browser (option 3)")
    
    print("\n🆘 Troubleshooting:")
    print("   - Ensure Chrome is running with --remote-debugging-port=9222")
    print("   - Check that .env file contains valid API key")
    print("   - Run installation test (option 5) to verify setup")
    print("   - Check README.md for detailed troubleshooting")


def main():
    """Main launcher function."""
    print_banner()
    
    # Basic checks
    if not check_installation():
        print("Please ensure all files are present and try again.")
        return
    
    # Check if dependencies are installed
    deps_installed = check_dependencies()
    if not deps_installed:
        print("⚠️  Dependencies not installed. Please run setup first (option 1).")
    
    # Check API keys
    check_api_keys()
    
    while True:
        show_menu()
        
        try:
            choice = input("\nEnter your choice (0-9): ").strip()
            
            if choice == "0":
                print("👋 Goodbye!")
                break
            elif choice == "1":
                run_setup()
            elif choice == "2":
                launch_chrome_with_debugging()
            elif choice == "3":
                if deps_installed:
                    run_cli_connect()
                else:
                    print("❌ Please install dependencies first (option 1)")
            elif choice == "4":
                if deps_installed:
                    run_cli_launch()
                else:
                    print("❌ Please install dependencies first (option 1)")
            elif choice == "5":
                if deps_installed:
                    run_test()
                else:
                    print("❌ Please install dependencies first (option 1)")
            elif choice == "6":
                if deps_installed:
                    run_examples()
                else:
                    print("❌ Please install dependencies first (option 1)")
            elif choice == "7":
                if deps_installed:
                    run_workflow_demo()
                else:
                    print("❌ Please install dependencies first (option 1)")
            elif choice == "8":
                if deps_installed:
                    print("\n💻 Starting CLI interactive mode...")
                    print("Choose connect (existing browser) or launch (new browser)")
                    subprocess.run([sys.executable, "-m", "browser_automation.cli", "--help"])
                else:
                    print("❌ Please install dependencies first (option 1)")
            elif choice == "9":
                show_help()
            else:
                print("❌ Invalid choice. Please enter 0-9.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
        
        input("\nPress Enter to continue...")


if __name__ == "__main__":
    main()
