@echo off
echo Starting Chrome with debugging enabled for Profile 2...
echo.

REM Kill any existing Chrome processes
taskkill /F /IM chrome.exe >nul 2>&1

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start Chrome with debugging
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\Users\<USER>\AppData\Local\Google\Chrome\User Data" --profile-directory="Profile 2" --no-first-run --no-default-browser-check

echo Chrome started with debugging enabled!
pause
