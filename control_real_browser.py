#!/usr/bin/env python3
"""
Control Your Real Browser - This will definitely work with your existing Chrome
"""

import subprocess
import time
import os
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys

def kill_chrome():
    """Kill all Chrome processes."""
    try:
        result = subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("🔄 Closed existing Chrome processes")
        time.sleep(2)
    except:
        pass

def start_chrome_for_automation():
    """Start Chrome with the exact settings for automation."""
    
    # Kill existing Chrome
    kill_chrome()
    
    # Chrome executable path
    chrome_exe = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    if not os.path.exists(chrome_exe):
        chrome_exe = r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    
    if not os.path.exists(chrome_exe):
        print("❌ Chrome not found!")
        return False
    
    # Your profile settings
    user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    profile_dir = "Profile 2"
    
    # Start Chrome with debugging
    cmd = [
        chrome_exe,
        "--remote-debugging-port=9222",
        f"--user-data-dir={user_data_dir}",
        f"--profile-directory={profile_dir}",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-blink-features=AutomationControlled",
        "--disable-extensions-except",
        "--disable-plugins-except"
    ]
    
    print("🚀 Starting Chrome with your Profile 2...")
    
    try:
        subprocess.Popen(cmd, creationflags=subprocess.CREATE_NO_WINDOW)
        print("⏳ Waiting for Chrome to start...")
        time.sleep(8)  # Give Chrome time to start
        return True
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        return False

def connect_to_chrome():
    """Connect Selenium to Chrome."""
    
    try:
        print("🔗 Connecting to Chrome...")
        
        # Chrome options
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "localhost:9222")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Connect to existing Chrome
        driver = webdriver.Chrome(options=chrome_options)
        
        print("✅ Successfully connected to your Chrome browser!")
        return driver
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("Trying alternative connection method...")
        
        # Alternative: Start new Chrome with your profile
        try:
            chrome_options = Options()
            chrome_options.add_argument(f"--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
            chrome_options.add_argument("--profile-directory=Profile 2")
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--no-default-browser-check")
            
            driver = webdriver.Chrome(options=chrome_options)
            print("✅ Connected using alternative method!")
            return driver
            
        except Exception as e2:
            print(f"❌ Alternative connection also failed: {e2}")
            return None

def test_browser_control(driver):
    """Test controlling your browser."""
    
    print("\n🧪 Testing browser control with your real sessions...")
    
    try:
        # Get current state
        current_url = driver.current_url
        print(f"📍 Current page: {current_url}")
        
        # Test navigation
        print("\n🌐 Testing navigation...")
        driver.get("https://www.google.com")
        time.sleep(3)
        
        title = driver.title
        print(f"✅ Navigated to: {title}")
        
        # Test search
        print("\n🔍 Testing search...")
        try:
            search_box = driver.find_element(By.NAME, "q")
            search_box.clear()
            search_box.send_keys("browser automation")
            search_box.send_keys(Keys.RETURN)
            time.sleep(3)
            print("✅ Search executed successfully!")
        except Exception as e:
            print(f"⚠️ Search test failed: {e}")
        
        # Check login status
        print("\n🔐 Checking login sessions...")
        driver.get("https://accounts.google.com")
        time.sleep(3)
        
        page_source = driver.page_source.lower()
        if "sign in" in page_source:
            print("🔓 Not logged in to Google")
        else:
            print("🔐 Appears to be logged in to Google!")
        
        return True
        
    except Exception as e:
        print(f"❌ Browser control test failed: {e}")
        return False

def interactive_control(driver):
    """Interactive browser control."""
    
    print("\n🎮 Interactive Browser Control")
    print("=" * 50)
    print("Your real browser is now under automation control!")
    print("All your login sessions and cookies are preserved.")
    print("\nCommands:")
    print("  go <url>        - Navigate to URL")
    print("  search <query>  - Search on Google")
    print("  click <text>    - Click element containing text")
    print("  type <text>     - Type text")
    print("  title           - Get page title")
    print("  url             - Get current URL")
    print("  screenshot      - Take screenshot")
    print("  gmail           - Go to Gmail")
    print("  facebook        - Go to Facebook")
    print("  quit            - Exit")
    
    while True:
        try:
            command = input("\n🎯 Enter command: ").strip()
            
            if command.lower() == "quit":
                break
                
            elif command.lower() == "title":
                print(f"📄 Title: {driver.title}")
                
            elif command.lower() == "url":
                print(f"📍 URL: {driver.current_url}")
                
            elif command.lower().startswith("go "):
                url = command[3:].strip()
                if not url.startswith("http"):
                    url = "https://" + url
                print(f"🌐 Navigating to: {url}")
                driver.get(url)
                time.sleep(2)
                print(f"✅ Loaded: {driver.title}")
                
            elif command.lower().startswith("search "):
                query = command[7:].strip()
                print(f"🔍 Searching for: {query}")
                driver.get("https://www.google.com")
                time.sleep(2)
                try:
                    search_box = driver.find_element(By.NAME, "q")
                    search_box.clear()
                    search_box.send_keys(query)
                    search_box.send_keys(Keys.RETURN)
                    time.sleep(3)
                    print("✅ Search completed!")
                except:
                    print("❌ Search failed")
                    
            elif command.lower() == "gmail":
                print("📧 Going to Gmail...")
                driver.get("https://mail.google.com")
                time.sleep(3)
                print("✅ Gmail loaded!")
                
            elif command.lower() == "facebook":
                print("📘 Going to Facebook...")
                driver.get("https://www.facebook.com")
                time.sleep(3)
                print("✅ Facebook loaded!")
                
            elif command.lower().startswith("click "):
                text = command[6:].strip()
                try:
                    element = driver.find_element(By.PARTIAL_LINK_TEXT, text)
                    element.click()
                    print(f"✅ Clicked: {text}")
                except:
                    try:
                        element = driver.find_element(By.XPATH, f"//*[contains(text(), '{text}')]")
                        element.click()
                        print(f"✅ Clicked: {text}")
                    except:
                        print(f"❌ Could not find: {text}")
                        
            elif command.lower().startswith("type "):
                text = command[5:].strip()
                try:
                    active_element = driver.switch_to.active_element
                    active_element.send_keys(text)
                    print(f"✅ Typed: {text}")
                except:
                    print("❌ No active input element")
                    
            elif command.lower() == "screenshot":
                filename = f"browser_control_{int(time.time())}.png"
                driver.save_screenshot(filename)
                print(f"📸 Screenshot saved: {filename}")
                
            else:
                print("❌ Unknown command. Type 'quit' to exit.")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    print("🎯 Real Browser Control Tool")
    print("=" * 40)
    print("This will control your existing Chrome browser with all login sessions!")
    
    # Step 1: Start Chrome
    print("\nStep 1: Starting Chrome with your profile...")
    if not start_chrome_for_automation():
        print("❌ Failed to start Chrome")
        return
    
    # Step 2: Connect to Chrome
    print("\nStep 2: Connecting to Chrome...")
    driver = connect_to_chrome()
    if not driver:
        print("❌ Failed to connect to Chrome")
        return
    
    # Step 3: Test control
    print("\nStep 3: Testing browser control...")
    if not test_browser_control(driver):
        print("❌ Browser control failed")
        driver.quit()
        return
    
    # Step 4: Success!
    print("\n🎉 SUCCESS! Your real browser is now under control!")
    print("✅ All your login sessions are preserved")
    print("✅ All cookies and saved data remain intact")
    print("✅ You can now automate any task")
    
    # Interactive control
    try:
        interactive_control(driver)
    except KeyboardInterrupt:
        pass
    
    print("\n👋 Closing browser control...")
    try:
        driver.quit()
    except:
        pass
    print("✅ Done!")

if __name__ == "__main__":
    main()
