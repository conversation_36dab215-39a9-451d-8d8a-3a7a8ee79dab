"""
Complete workflow example demonstrating all features of browser session automation.
"""

import asyncio
import os
import json
from pathlib import Path
import sys
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from browser_automation import BrowserSessionManager, AutomationEngine, Config


async def demonstrate_complete_workflow():
    """
    Comprehensive demonstration of browser session automation capabilities.
    """
    
    print("🚀 Complete Browser Session Automation Workflow")
    print("="*60)
    
    # Step 1: Configuration Setup
    print("\n📋 Step 1: Setting up configuration...")
    config = Config()
    
    # Configure for your specific Chrome installation
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    config.cdp.port = 9222
    config.session.auto_save_interval = 60  # Save every minute for demo
    
    print(f"✅ Configuration set up:")
    print(f"   - User Data Dir: {config.browser.user_data_dir}")
    print(f"   - Profile: {config.browser.profile_name}")
    print(f"   - CDP Port: {config.cdp.port}")
    
    # Step 2: Session Management
    print("\n🔐 Step 2: Creating session manager...")
    session_id = f"complete_workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    session_manager = BrowserSessionManager(config, session_id=session_id)
    
    print(f"✅ Session manager created with ID: {session_id}")
    
    # Step 3: Browser Connection
    print("\n🌐 Step 3: Connecting to browser...")
    
    # Try existing browser first
    connected = await session_manager.connect_to_existing_browser()
    
    if not connected:
        print("   No existing browser found, launching new one...")
        connected = await session_manager.launch_new_browser()
    
    if not connected:
        print("❌ Failed to connect to browser. Please ensure Chrome is available.")
        return
    
    print("✅ Successfully connected to browser!")
    print(f"   - Browser connected: {session_manager.is_connected}")
    print(f"   - Current URL: {session_manager.page.url if session_manager.page else 'N/A'}")
    
    # Step 4: Automation Engine Setup
    print("\n🤖 Step 4: Initializing automation engine...")
    
    # Check for API keys
    api_keys = {
        'OpenAI': os.getenv('OPENAI_API_KEY'),
        'Anthropic': os.getenv('ANTHROPIC_API_KEY'),
        'Gemini': os.getenv('GEMINI_API_KEY')
    }
    
    available_keys = [k for k, v in api_keys.items() if v]
    
    if not available_keys:
        print("⚠️  No LLM API keys found. Some automation features will be limited.")
        print("   Set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GEMINI_API_KEY for full functionality.")
    else:
        print(f"✅ Available LLM providers: {', '.join(available_keys)}")
    
    engine = AutomationEngine(session_manager)
    await engine.initialize()
    print("✅ Automation engine initialized!")
    
    # Step 5: Session State Management
    print("\n💾 Step 5: Demonstrating session state management...")
    
    # Store some test data
    session_manager.store_auth_token("demo_token", "abc123xyz789")
    session_manager.store_auth_token("user_id", "demo_user_12345")
    
    print("✅ Stored authentication tokens:")
    print(f"   - demo_token: {session_manager.get_auth_token('demo_token')}")
    print(f"   - user_id: {session_manager.get_auth_token('user_id')}")
    
    # Save session state
    await session_manager.save_session_state()
    print("✅ Session state saved to encrypted storage")
    
    # Step 6: Basic Navigation and Automation
    print("\n🧭 Step 6: Demonstrating navigation and automation...")
    
    # Navigate to a test site
    print("   Navigating to httpbin.org...")
    success = await engine.navigate_to_url("https://httpbin.org")
    
    if success:
        print("✅ Navigation successful!")
        
        # Get page information
        title = await engine.get_page_title()
        current_url = await engine.get_current_url()
        
        print(f"   - Page title: {title}")
        print(f"   - Current URL: {current_url}")
        
        # Take a screenshot
        print("   Taking screenshot...")
        screenshot_path = f"workflow_screenshot_{session_id}.png"
        await engine.take_screenshot(screenshot_path)
        print(f"✅ Screenshot saved: {screenshot_path}")
        
    else:
        print("❌ Navigation failed")
    
    # Step 7: Advanced Automation (if API key available)
    if available_keys:
        print("\n🎯 Step 7: Demonstrating advanced automation...")
        
        # Navigate to a form page
        print("   Navigating to form example...")
        await engine.navigate_to_url("https://httpbin.org/forms/post")
        
        # Perform automated form interaction
        print("   Executing form automation task...")
        task = """
        Look at this form and fill it out with sample data:
        - Customer name: Demo User
        - Telephone: 555-0123
        - Email: <EMAIL>
        - Pizza size: Medium
        - Toppings: Cheese
        - Delivery time: 6:00 PM
        - Comments: This is a test automation
        
        Do NOT submit the form, just fill it out.
        """
        
        result = await engine.execute_task(task)
        
        if result['success']:
            print("✅ Form automation completed successfully!")
        else:
            print(f"❌ Form automation failed: {result.get('error', 'Unknown error')}")
    
    # Step 8: Cookie and Storage Management
    print("\n🍪 Step 8: Demonstrating cookie and storage management...")
    
    # Set some test cookies and local storage
    await engine.execute_javascript("""
        // Set test cookies
        document.cookie = "demo_session=workflow_test_123; path=/";
        document.cookie = "user_pref=dark_mode; path=/";
        
        // Set local storage
        localStorage.setItem('demo_key', 'demo_value');
        localStorage.setItem('last_visit', new Date().toISOString());
    """)
    
    print("✅ Set test cookies and local storage")
    
    # Save session state to capture cookies and storage
    await session_manager.save_session_state()
    print("✅ Session state saved with cookies and storage")
    
    # Step 9: Session Persistence Demonstration
    print("\n🔄 Step 9: Demonstrating session persistence...")
    
    # List all stored sessions
    stored_sessions = session_manager.list_stored_sessions()
    print(f"✅ Found {len(stored_sessions)} stored sessions:")
    for session in stored_sessions[-5:]:  # Show last 5
        print(f"   - {session}")
    
    # Step 10: Error Handling and Recovery
    print("\n🛡️ Step 10: Demonstrating error handling...")
    
    try:
        # Test JavaScript execution
        result = await engine.execute_javascript("return document.title;")
        print(f"✅ JavaScript execution successful: {result}")
        
        # Test element waiting (with short timeout)
        element_found = await engine.wait_for_element("body", timeout=5000)
        print(f"✅ Element waiting works: {element_found}")
        
    except Exception as e:
        print(f"⚠️  Error handling test: {e}")
    
    # Step 11: Cleanup and Summary
    print("\n🧹 Step 11: Cleanup and summary...")
    
    # Final session save
    await session_manager.save_session_state()
    
    # Generate summary
    summary = {
        'session_id': session_id,
        'timestamp': datetime.now().isoformat(),
        'browser_connected': session_manager.is_connected,
        'final_url': await engine.get_current_url() if session_manager.page else None,
        'stored_tokens': len(session_manager._session_data.get('auth_tokens', {})),
        'api_keys_available': available_keys,
        'screenshots_taken': 1,
        'automation_tasks_completed': 1 if available_keys else 0
    }
    
    # Save summary to file
    summary_file = f"workflow_summary_{session_id}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print("✅ Workflow summary:")
    for key, value in summary.items():
        print(f"   - {key}: {value}")
    
    print(f"✅ Summary saved to: {summary_file}")
    
    # Close resources
    await engine.close()
    await session_manager.close()
    
    print("\n🎉 Complete workflow demonstration finished!")
    print("="*60)
    
    print("\n📋 What was demonstrated:")
    print("✅ Configuration management")
    print("✅ Session creation and management")
    print("✅ Browser connection (existing or new)")
    print("✅ Automation engine initialization")
    print("✅ Secure credential storage")
    print("✅ Navigation and basic automation")
    print("✅ Advanced LLM-powered automation (if API key available)")
    print("✅ Cookie and local storage management")
    print("✅ Session persistence")
    print("✅ Error handling and recovery")
    print("✅ Cleanup and resource management")
    
    print(f"\n📁 Files created:")
    print(f"   - {screenshot_path}")
    print(f"   - {summary_file}")
    
    print(f"\n🔐 Session data stored with ID: {session_id}")
    print("   Run this script again to see session persistence in action!")


async def main():
    """Main function."""
    try:
        await demonstrate_complete_workflow()
    except KeyboardInterrupt:
        print("\n\n⚠️  Workflow interrupted by user")
    except Exception as e:
        print(f"\n❌ Workflow failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
