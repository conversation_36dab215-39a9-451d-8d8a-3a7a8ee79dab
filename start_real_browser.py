#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to properly start your real Chrome browser with debugging enabled
and then connect the automation system to it.
"""

import subprocess
import time
import requests
import sys
import asyncio
from pathlib import Path

def kill_chrome():
    """Kill all Chrome processes."""
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                      capture_output=True, check=False)
        print("🔄 Closed existing Chrome processes")
        time.sleep(2)
    except:
        pass

def start_chrome_with_debugging():
    """Start Chrome with debugging enabled using your Profile 2."""
    
    # Kill existing Chrome first
    kill_chrome()
    
    # Chrome command with your exact profile
    cmd = [
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "--remote-debugging-port=9222",
        "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data",
        "--profile-directory=Profile 2",
        "--no-first-run",
        "--no-default-browser-check"
    ]
    
    print("🚀 Starting Chrome with debugging enabled...")
    print("   Profile: Profile 2")
    print("   Debugging Port: 9222")
    
    try:
        # Start Chrome in background
        subprocess.Popen(cmd, 
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL)
        
        print("⏳ Waiting for Chrome to start...")
        
        # Wait for Chrome to be ready
        for i in range(15):
            time.sleep(1)
            try:
                response = requests.get("http://localhost:9222/json/version", timeout=2)
                if response.status_code == 200:
                    print("✅ Chrome started successfully with debugging!")
                    return True
            except:
                pass
            print(f"   Waiting... ({i+1}/15)")
        
        print("❌ Chrome failed to start with debugging")
        return False
        
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        return False

def check_chrome_tabs():
    """Check what tabs are open in Chrome."""
    try:
        response = requests.get("http://localhost:9222/json", timeout=5)
        tabs = response.json()
        
        print(f"\n📑 Found {len(tabs)} browser tabs:")
        for i, tab in enumerate(tabs[:5]):
            title = tab.get('title', 'No title')[:50]
            url = tab.get('url', 'No URL')[:60]
            print(f"   {i+1}. {title}")
            print(f"      {url}")
        
        if len(tabs) > 5:
            print(f"   ... and {len(tabs) - 5} more tabs")
        
        return True
    except Exception as e:
        print(f"❌ Failed to get tab info: {e}")
        return False

async def test_automation():
    """Test automation with the real browser."""
    print("\n🤖 Testing automation connection...")
    
    try:
        from browser_automation import BrowserSessionManager, Config
        
        # Create config
        config = Config()
        
        # Create session manager
        session_manager = BrowserSessionManager(config, "real_browser_connection")
        
        # Try to connect
        print("🔗 Connecting to your real browser...")
        success = await session_manager.connect_to_existing_browser()
        
        if success:
            print("✅ Successfully connected to your real browser!")
            
            # Get current URL
            current_url = await session_manager.get_current_url()
            print(f"📍 Current page: {current_url}")
            
            print("\n🎉 Your real browser is now connected and ready for automation!")
            print("\nYou can now use commands like:")
            print("   python -m browser_automation.cli connect")
            print("   python -m browser_automation.cli execute \"your task here\"")
            
        else:
            print("❌ Failed to connect to browser")
            
        await session_manager.close()
        
    except Exception as e:
        print(f"❌ Error testing automation: {e}")

def main():
    print("🌐 Real Browser Automation Setup")
    print("=" * 40)
    
    # Step 1: Start Chrome with debugging
    if not start_chrome_with_debugging():
        print("\n❌ Failed to start Chrome. Please try manually:")
        print('   "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data" --profile-directory="Profile 2"')
        return
    
    # Step 2: Check tabs
    if not check_chrome_tabs():
        return
    
    # Step 3: Test automation
    print("\n" + "=" * 40)
    asyncio.run(test_automation())
    
    print("\n🎉 Setup complete! Your real browser is ready for automation.")

if __name__ == "__main__":
    main()
