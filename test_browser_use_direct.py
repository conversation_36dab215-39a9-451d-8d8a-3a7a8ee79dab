#!/usr/bin/env python3
"""
Test browser-use directly with your profile to see if it can work with your real browser.
"""

import asyncio
from browser_use import Agent, <PERSON><PERSON><PERSON>, ChatGoogle
from dotenv import load_dotenv

load_dotenv()

async def test_with_your_profile():
    """Test browser-use with your Chrome profile."""
    
    print("🌐 Testing browser-use with your Chrome Profile 2...")
    
    try:
        # Create browser instance with your profile
        browser = Browser(
            user_data_dir=r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data",
            profile_directory="Profile 2",
            headless=False,
            # Enable debugging
            args=["--remote-debugging-port=9222"]
        )
        
        print("✅ Browser instance created")
        
        # Create LLM
        llm = ChatGoogle(model="gemini-1.5-flash")
        print("✅ LLM created")
        
        # Create agent
        agent = Agent(
            task="Navigate to https://httpbin.org and tell me what you see on the page",
            browser=browser,
            llm=llm
        )
        
        print("✅ Agent created")
        print("🚀 Starting automation task...")
        
        # Run the task
        result = await agent.run()
        
        print("✅ Task completed!")
        print(f"🤖 Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_simple_navigation():
    """Test simple navigation with your profile."""
    
    print("\n🧭 Testing simple navigation...")
    
    try:
        # Create browser instance with your profile
        browser = Browser(
            user_data_dir=r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data",
            profile_directory="Profile 2",
            headless=False
        )
        
        print("✅ Browser created with your profile")
        
        # Create a simple LLM task
        llm = ChatGoogle(model="gemini-1.5-flash")
        
        # Simple navigation task
        agent = Agent(
            task="Go to google.com",
            browser=browser,
            llm=llm
        )
        
        print("🚀 Navigating to Google...")
        result = await agent.run()
        
        print("✅ Navigation completed!")
        print(f"🤖 Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Navigation error: {e}")
        return False

def main():
    print("🎯 Direct Browser-Use Test with Your Real Profile")
    print("=" * 60)
    
    # Test 1: Basic functionality
    print("\n📋 Test 1: Basic browser automation")
    success1 = asyncio.run(test_with_your_profile())
    
    if success1:
        print("\n📋 Test 2: Simple navigation")
        success2 = asyncio.run(test_simple_navigation())
        
        if success2:
            print("\n🎉 SUCCESS! Browser-use is working with your real Chrome profile!")
            print("\n📝 Your real browser sessions are preserved and automation is working!")
            print("\n🔧 You can now use browser-use directly with your profile:")
            print("   - All your login sessions are preserved")
            print("   - All cookies and saved data remain")
            print("   - Automation works with natural language")
        else:
            print("\n⚠️  Basic test passed but navigation failed")
    else:
        print("\n❌ Browser-use test failed")
        print("   This might be due to API key or browser configuration issues")

if __name__ == "__main__":
    main()
