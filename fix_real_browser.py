#!/usr/bin/env python3
"""
Fix Real Browser Connection - This will definitely connect to your existing browser
"""

import subprocess
import time
import requests
import asyncio
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def find_chrome_executable():
    """Find Chrome executable path."""
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def kill_all_chrome():
    """Kill all Chrome processes."""
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                      capture_output=True, check=False)
        print("🔄 Closed all Chrome processes")
        time.sleep(2)
    except:
        pass

def start_chrome_with_debugging():
    """Start Chrome with debugging enabled using your exact profile."""
    
    chrome_exe = find_chrome_executable()
    if not chrome_exe:
        print("❌ Chrome not found!")
        return False
    
    # Kill existing Chrome first
    kill_all_chrome()
    
    # Your exact profile path
    user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    profile_dir = "Profile 2"
    
    # Command to start Chrome with debugging
    cmd = [
        chrome_exe,
        f"--remote-debugging-port=9222",
        f"--user-data-dir={user_data_dir}",
        f"--profile-directory={profile_dir}",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor"
    ]
    
    print("🚀 Starting Chrome with debugging...")
    print(f"   Profile: {profile_dir}")
    print(f"   User Data: {user_data_dir}")
    
    try:
        # Start Chrome
        process = subprocess.Popen(cmd, 
                                 creationflags=subprocess.CREATE_NO_WINDOW,
                                 stdout=subprocess.DEVNULL,
                                 stderr=subprocess.DEVNULL)
        
        print("⏳ Waiting for Chrome to initialize...")
        
        # Wait for Chrome to be ready
        for i in range(20):
            time.sleep(1)
            try:
                response = requests.get("http://localhost:9222/json/version", timeout=2)
                if response.status_code == 200:
                    print("✅ Chrome debugging port is active!")
                    return True
            except:
                pass
            print(f"   Waiting... ({i+1}/20)")
        
        print("❌ Chrome debugging port failed to start")
        return False
        
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        return False

def connect_selenium_to_existing_chrome():
    """Connect Selenium to the existing Chrome with debugging."""
    
    try:
        # Chrome options for connecting to existing instance
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "localhost:9222")
        
        # Create WebDriver connected to existing Chrome
        driver = webdriver.Chrome(options=chrome_options)
        
        print("✅ Connected to your existing Chrome browser!")
        return driver
        
    except Exception as e:
        print(f"❌ Failed to connect to Chrome: {e}")
        return None

def test_real_browser_control(driver):
    """Test controlling your real browser."""
    
    try:
        print("\n🧪 Testing browser control...")
        
        # Get current URL
        current_url = driver.current_url
        print(f"📍 Current page: {current_url}")
        
        # Test 1: Navigate to a test site
        print("\n🌐 Test 1: Navigation")
        driver.get("https://httpbin.org")
        time.sleep(3)
        
        # Get page title
        title = driver.title
        print(f"✅ Navigated to: {title}")
        
        # Test 2: Check if you have any login sessions
        print("\n🔐 Test 2: Session check")
        driver.get("https://accounts.google.com")
        time.sleep(3)
        
        # Check if logged in (look for profile or login elements)
        try:
            # Look for signs of being logged in
            WebDriverWait(driver, 5).until(
                lambda d: d.find_element(By.TAG_NAME, "body")
            )
            
            page_source = driver.page_source.lower()
            if "sign in" in page_source or "login" in page_source:
                print("🔓 Not logged in to Google (or login page)")
            else:
                print("🔐 Appears to be logged in to Google!")
                
        except:
            print("⚠️  Could not determine login status")
        
        # Test 3: Interactive control
        print("\n🎯 Test 3: Interactive control")
        print("Your browser is now under automation control!")
        print("You should see it navigating automatically.")
        
        # Go back to a neutral page
        driver.get("https://httpbin.org")
        time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ Browser control test failed: {e}")
        return False

def interactive_browser_control(driver):
    """Interactive browser control session."""
    
    print("\n🎮 Interactive Browser Control")
    print("=" * 40)
    print("Commands:")
    print("  go <url>     - Navigate to URL")
    print("  title        - Get page title")
    print("  url          - Get current URL")
    print("  click <text> - Click element containing text")
    print("  type <text>  - Type text into focused element")
    print("  screenshot   - Take screenshot")
    print("  quit         - Exit")
    
    while True:
        try:
            command = input("\n🎯 Enter command: ").strip().lower()
            
            if command == "quit":
                break
            elif command == "title":
                print(f"📄 Title: {driver.title}")
            elif command == "url":
                print(f"📍 URL: {driver.current_url}")
            elif command.startswith("go "):
                url = command[3:].strip()
                if not url.startswith("http"):
                    url = "https://" + url
                print(f"🌐 Navigating to: {url}")
                driver.get(url)
                time.sleep(2)
                print(f"✅ Loaded: {driver.title}")
            elif command.startswith("click "):
                text = command[6:].strip()
                try:
                    element = driver.find_element(By.PARTIAL_LINK_TEXT, text)
                    element.click()
                    print(f"✅ Clicked element containing: {text}")
                except:
                    try:
                        element = driver.find_element(By.XPATH, f"//*[contains(text(), '{text}')]")
                        element.click()
                        print(f"✅ Clicked element containing: {text}")
                    except:
                        print(f"❌ Could not find element containing: {text}")
            elif command.startswith("type "):
                text = command[5:].strip()
                try:
                    active_element = driver.switch_to.active_element
                    active_element.send_keys(text)
                    print(f"✅ Typed: {text}")
                except:
                    print("❌ No active input element")
            elif command == "screenshot":
                filename = f"browser_screenshot_{int(time.time())}.png"
                driver.save_screenshot(filename)
                print(f"📸 Screenshot saved: {filename}")
            else:
                print("❌ Unknown command")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    print("🔧 Real Browser Connection Fix")
    print("=" * 40)
    
    # Step 1: Start Chrome with debugging
    print("Step 1: Starting Chrome with debugging...")
    if not start_chrome_with_debugging():
        print("❌ Failed to start Chrome with debugging")
        return
    
    # Step 2: Connect Selenium to existing Chrome
    print("\nStep 2: Connecting to your Chrome browser...")
    driver = connect_selenium_to_existing_chrome()
    if not driver:
        print("❌ Failed to connect to Chrome")
        return
    
    # Step 3: Test browser control
    print("\nStep 3: Testing browser control...")
    if not test_real_browser_control(driver):
        print("❌ Browser control test failed")
        driver.quit()
        return
    
    # Step 4: Interactive control
    print("\n🎉 SUCCESS! Your real browser is now under control!")
    print("All your login sessions and cookies are preserved!")
    
    choice = input("\nWould you like to try interactive control? (y/n): ").strip().lower()
    if choice == 'y':
        interactive_browser_control(driver)
    
    print("\n👋 Closing browser control...")
    driver.quit()
    print("✅ Done!")

if __name__ == "__main__":
    main()
