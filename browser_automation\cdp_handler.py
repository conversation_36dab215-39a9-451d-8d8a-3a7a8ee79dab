"""
Chrome DevTools Protocol connection handler with robust reconnection logic.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, Callable, List
import websockets
import psutil
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontext, Page

from .exceptions import ConnectionError, BrowserNotFoundError
from .config import Config


logger = logging.getLogger(__name__)


class CDPHandler:
    """Handles Chrome DevTools Protocol connections with reconnection logic."""
    
    def __init__(self, config: Config):
        self.config = config
        self.cdp_config = config.cdp
        
        self._browser: Optional[Browser] = None
        self._context: Optional[BrowserContext] = None
        self._page: Optional[Page] = None
        self._playwright = None
        
        self._connected = False
        self._reconnect_task: Optional[asyncio.Task] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        
        self._event_handlers: Dict[str, List[Callable]] = {}
        
    async def connect_to_existing_browser(self) -> bool:
        """Connect to an existing browser instance via CDP."""
        try:
            # Find existing Chrome/Chromium process
            chrome_process = self._find_chrome_process()
            if not chrome_process:
                raise BrowserNotFoundError("No Chrome/Chromium process found with remote debugging enabled")
            
            # Connect using Playwright
            self._playwright = await async_playwright().start()
            
            # Try to connect to existing browser
            cdp_url = f"http://{self.cdp_config.host}:{self.cdp_config.port}"
            
            try:
                self._browser = await self._playwright.chromium.connect_over_cdp(cdp_url)
                logger.info(f"Connected to existing browser via CDP: {cdp_url}")
                
                # Get existing context or create new one
                contexts = self._browser.contexts
                if contexts:
                    self._context = contexts[0]
                    logger.info("Using existing browser context")
                else:
                    self._context = await self._browser.new_context()
                    logger.info("Created new browser context")
                
                # Get existing page or create new one
                pages = self._context.pages
                if pages:
                    self._page = pages[0]
                    logger.info("Using existing page")
                else:
                    self._page = await self._context.new_page()
                    logger.info("Created new page")
                
                self._connected = True
                
                # Start heartbeat monitoring
                await self._start_heartbeat()
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to connect via CDP: {e}")
                await self._cleanup()
                return False
                
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def _find_chrome_process(self) -> Optional[psutil.Process]:
        """Find Chrome/Chromium process with remote debugging enabled."""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline'] or []
                    cmdline_str = ' '.join(cmdline)
                    
                    # Check if remote debugging is enabled
                    if f'--remote-debugging-port={self.cdp_config.port}' in cmdline_str:
                        logger.info(f"Found Chrome process with remote debugging: PID {proc.info['pid']}")
                        return proc
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return None
    
    async def launch_browser_with_debugging(self) -> bool:
        """Launch a new browser instance with remote debugging enabled."""
        try:
            self._playwright = await async_playwright().start()
            
            # Browser launch arguments
            args = [
                f'--remote-debugging-port={self.cdp_config.port}',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
            ]
            
            # Add user data directory if specified
            if self.config.browser.user_data_dir:
                args.append(f'--user-data-dir={self.config.browser.user_data_dir}')
            
            # Add custom args
            args.extend(self.config.browser.args)
            
            # Launch browser
            self._browser = await self._playwright.chromium.launch(
                headless=self.config.browser.headless,
                args=args,
                executable_path=self.config.browser.executable_path,
            )
            
            self._context = await self._browser.new_context(
                viewport={'width': self.config.browser.window_size[0], 
                         'height': self.config.browser.window_size[1]}
            )
            
            self._page = await self._context.new_page()
            
            self._connected = True
            logger.info("Launched new browser with remote debugging")
            
            # Start heartbeat monitoring
            await self._start_heartbeat()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to launch browser: {e}")
            await self._cleanup()
            return False
    
    async def _start_heartbeat(self) -> None:
        """Start heartbeat monitoring to detect connection issues."""
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            
        self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
    
    async def _heartbeat_loop(self) -> None:
        """Heartbeat loop to monitor connection health."""
        while self._connected:
            try:
                await asyncio.sleep(self.cdp_config.heartbeat_interval)
                
                if self._page:
                    # Simple health check
                    await self._page.evaluate("() => true")
                    
            except Exception as e:
                logger.warning(f"Heartbeat failed: {e}")
                self._connected = False
                
                # Start reconnection
                if not self._reconnect_task or self._reconnect_task.done():
                    self._reconnect_task = asyncio.create_task(self._reconnect_loop())
                break
    
    async def _reconnect_loop(self) -> None:
        """Reconnection loop with exponential backoff."""
        retry_count = 0
        base_delay = self.cdp_config.retry_delay
        
        while retry_count < self.cdp_config.max_retries and not self._connected:
            try:
                logger.info(f"Attempting reconnection {retry_count + 1}/{self.cdp_config.max_retries}")
                
                # Clean up existing connections
                await self._cleanup(keep_playwright=True)
                
                # Try to reconnect
                if await self.connect_to_existing_browser():
                    logger.info("Reconnection successful")
                    return
                    
            except Exception as e:
                logger.error(f"Reconnection attempt {retry_count + 1} failed: {e}")
            
            retry_count += 1
            if retry_count < self.cdp_config.max_retries:
                delay = base_delay * (2 ** retry_count)  # Exponential backoff
                logger.info(f"Waiting {delay}s before next reconnection attempt")
                await asyncio.sleep(delay)
        
        logger.error("All reconnection attempts failed")
        await self._cleanup()
    
    async def get_cookies(self) -> List[Dict[str, Any]]:
        """Get all cookies from the current context."""
        if not self._context:
            raise ConnectionError("No browser context available")
            
        try:
            cookies = await self._context.cookies()
            return [cookie for cookie in cookies]
        except Exception as e:
            raise ConnectionError(f"Failed to get cookies: {e}")
    
    async def set_cookies(self, cookies: List[Dict[str, Any]]) -> None:
        """Set cookies in the current context."""
        if not self._context:
            raise ConnectionError("No browser context available")
            
        try:
            await self._context.add_cookies(cookies)
        except Exception as e:
            raise ConnectionError(f"Failed to set cookies: {e}")
    
    async def get_local_storage(self, url: str) -> Dict[str, str]:
        """Get local storage data for a specific URL."""
        if not self._page:
            raise ConnectionError("No page available")
            
        try:
            await self._page.goto(url)
            local_storage = await self._page.evaluate("""
                () => {
                    const storage = {};
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        storage[key] = localStorage.getItem(key);
                    }
                    return storage;
                }
            """)
            return local_storage
        except Exception as e:
            raise ConnectionError(f"Failed to get local storage: {e}")
    
    async def set_local_storage(self, url: str, storage_data: Dict[str, str]) -> None:
        """Set local storage data for a specific URL."""
        if not self._page:
            raise ConnectionError("No page available")
            
        try:
            await self._page.goto(url)
            await self._page.evaluate("""
                (storageData) => {
                    for (const [key, value] of Object.entries(storageData)) {
                        localStorage.setItem(key, value);
                    }
                }
            """, storage_data)
        except Exception as e:
            raise ConnectionError(f"Failed to set local storage: {e}")
    
    async def get_session_storage(self, url: str) -> Dict[str, str]:
        """Get session storage data for a specific URL."""
        if not self._page:
            raise ConnectionError("No page available")
            
        try:
            await self._page.goto(url)
            session_storage = await self._page.evaluate("""
                () => {
                    const storage = {};
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        storage[key] = sessionStorage.getItem(key);
                    }
                    return storage;
                }
            """)
            return session_storage
        except Exception as e:
            raise ConnectionError(f"Failed to get session storage: {e}")
    
    @property
    def browser(self) -> Optional[Browser]:
        """Get the browser instance."""
        return self._browser
    
    @property
    def context(self) -> Optional[BrowserContext]:
        """Get the browser context."""
        return self._context
    
    @property
    def page(self) -> Optional[Page]:
        """Get the current page."""
        return self._page
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to browser."""
        return self._connected
    
    async def _cleanup(self, keep_playwright: bool = False) -> None:
        """Clean up resources."""
        self._connected = False
        
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            self._heartbeat_task = None
        
        if self._page:
            try:
                await self._page.close()
            except:
                pass
            self._page = None
        
        if self._context:
            try:
                await self._context.close()
            except:
                pass
            self._context = None
        
        if self._browser:
            try:
                await self._browser.close()
            except:
                pass
            self._browser = None
        
        if not keep_playwright and self._playwright:
            try:
                await self._playwright.stop()
            except:
                pass
            self._playwright = None
    
    async def close(self) -> None:
        """Close all connections and clean up."""
        if self._reconnect_task:
            self._reconnect_task.cancel()
        
        await self._cleanup()
