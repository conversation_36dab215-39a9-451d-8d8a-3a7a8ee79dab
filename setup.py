"""
Setup script for browser session automation.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is 3.11+"""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def install_playwright():
    """Install Playwright browsers"""
    print("\n🎭 Installing Playwright browsers...")
    try:
        subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], check=True)
        print("✅ Playwright browsers installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Playwright browsers: {e}")
        return False


def create_env_file():
    """Create .env file if it doesn't exist"""
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    print("\n📝 Creating .env file...")
    env_content = """# Browser Session Automation Configuration
# Choose one of the following LLM providers:

# OpenAI (recommended)
# OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini
# GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Custom browser settings
# CHROME_EXECUTABLE_PATH=C:/Program Files/Google/Chrome/Application/chrome.exe
# CHROME_USER_DATA_DIR=C:/Users/<USER>/AppData/Local/Google/Chrome/User Data
# CHROME_PROFILE_NAME=Profile 2
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✅ .env file created")
        print("⚠️  Please edit .env file and add your LLM API key")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False


def check_chrome_installation():
    """Check if Chrome is installed"""
    print("\n🌐 Checking Chrome installation...")
    
    chrome_paths = [
        "C:/Program Files/Google/Chrome/Application/chrome.exe",
        "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser"
    ]
    
    for path in chrome_paths:
        if Path(path).exists():
            print(f"✅ Chrome found at: {path}")
            return True
    
    print("⚠️  Chrome not found in standard locations")
    print("Please ensure Chrome/Chromium is installed")
    return False


def create_example_config():
    """Create example configuration file"""
    print("\n⚙️ Creating example configuration...")
    
    config_content = """# Example configuration for browser automation
browser:
  # Update these paths to match your system
  user_data_dir: "C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
  profile_name: "Profile 2"
  headless: false
  window_size: [1920, 1080]
  args:
    - "--disable-web-security"
    - "--disable-features=VizDisplayCompositor"

cdp:
  host: "localhost"
  port: 9222
  timeout: 30
  max_retries: 3
  retry_delay: 1.0
  heartbeat_interval: 30.0

session:
  auto_save_interval: 300  # seconds
  max_session_age: 604800  # 7 days
  backup_count: 5

logging:
  level: "INFO"
  file_path: "automation.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
"""
    
    config_file = Path("config.example.yaml")
    try:
        with open(config_file, 'w') as f:
            f.write(config_content)
        print(f"✅ Example configuration created: {config_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create example config: {e}")
        return False


def run_basic_test():
    """Run a basic test to verify installation"""
    print("\n🧪 Running basic test...")
    
    try:
        # Test imports
        from browser_automation import BrowserSessionManager, AutomationEngine, Config
        print("✅ Imports successful")
        
        # Test configuration
        config = Config()
        print("✅ Configuration creation successful")
        
        # Test session manager creation
        session_manager = BrowserSessionManager(config)
        print("✅ Session manager creation successful")
        
        print("✅ Basic test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic test failed: {e}")
        return False


def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 Setup completed successfully!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print("1. Edit .env file and add your LLM API key")
    print("2. Update browser paths in config.example.yaml if needed")
    print("3. Launch Chrome with debugging:")
    print('   "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222')
    print("4. Run the examples:")
    print("   python examples/basic_usage.py")
    print("5. Or use the CLI:")
    print("   python -m browser_automation.cli connect")
    
    print("\n📚 Documentation:")
    print("- README.md - Complete documentation")
    print("- examples/ - Usage examples")
    print("- config.example.yaml - Configuration reference")
    
    print("\n🆘 Need help?")
    print("- Check README.md for troubleshooting")
    print("- Run with --debug flag for detailed logs")
    print("- Ensure Chrome is running with remote debugging enabled")


def main():
    """Main setup function"""
    print("🤖 Browser Session Automation Setup")
    print("="*50)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Install dependencies
    if success and not install_dependencies():
        success = False
    
    # Install Playwright
    if success and not install_playwright():
        success = False
    
    # Create .env file
    if success and not create_env_file():
        success = False
    
    # Check Chrome installation
    check_chrome_installation()  # Non-blocking
    
    # Create example config
    if success and not create_example_config():
        success = False
    
    # Run basic test
    if success and not run_basic_test():
        success = False
    
    if success:
        print_next_steps()
    else:
        print("\n❌ Setup failed. Please check the errors above and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
