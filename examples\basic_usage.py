"""
Basic usage example for browser session automation.
"""

import asyncio
import os
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from browser_automation import <PERSON>rowser<PERSON>essionManager, AutomationEngine, Config


async def basic_example():
    """Basic example showing connection and simple automation."""
    
    # Create configuration
    config = Config()
    
    # Set your Chrome profile path (update this to match your system)
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    # Create session manager
    session_manager = BrowserSessionManager(config, session_id="basic_example")
    
    try:
        print("🔗 Connecting to existing browser...")
        
        # Try to connect to existing browser first
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            print("🚀 Launching new browser with debugging...")
            connected = await session_manager.launch_new_browser()
        
        if not connected:
            print("❌ Failed to connect or launch browser")
            return
        
        print("✅ Connected to browser successfully!")
        print(f"Session ID: {session_manager.session_id}")
        
        # Create automation engine
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        
        print("\n📍 Navigating to Google...")
        await engine.navigate_to_url("https://www.google.com")
        
        print("\n🔍 Performing a search...")
        result = await engine.execute_task("Search for 'browser automation' on Google")
        
        if result['success']:
            print("✅ Search completed successfully!")
            print(f"Final URL: {result.get('final_url')}")
        else:
            print(f"❌ Search failed: {result.get('error')}")
        
        print("\n📸 Taking a screenshot...")
        screenshot_path = "search_results.png"
        await engine.take_screenshot(screenshot_path)
        print(f"Screenshot saved to: {screenshot_path}")
        
        print("\n💾 Saving session state...")
        await session_manager.save_session_state()
        print("Session state saved!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        print("\n🧹 Cleaning up...")
        if 'engine' in locals():
            await engine.close()
        await session_manager.close()
        print("Cleanup complete!")


async def form_filling_example():
    """Example showing form filling automation."""
    
    config = Config()
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    session_manager = BrowserSessionManager(config, session_id="form_example")
    
    try:
        print("🔗 Connecting to browser...")
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            connected = await session_manager.launch_new_browser()
        
        if not connected:
            print("❌ Failed to connect to browser")
            return
        
        print("✅ Connected!")
        
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        
        print("\n📝 Navigating to a form example...")
        await engine.navigate_to_url("https://httpbin.org/forms/post")
        
        print("\n✍️ Filling out the form...")
        task = """
        Fill out the form with the following information:
        - Customer name: John Doe
        - Telephone: ************
        - Email: <EMAIL>
        - Pizza size: Large
        - Toppings: Pepperoni and Mushrooms
        - Delivery time: 7:00 PM
        - Comments: Please ring the doorbell
        Then submit the form.
        """
        
        result = await engine.execute_task(task)
        
        if result['success']:
            print("✅ Form filled and submitted successfully!")
        else:
            print(f"❌ Form filling failed: {result.get('error')}")
        
        await session_manager.save_session_state()
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.close()
        await session_manager.close()


async def session_persistence_example():
    """Example showing session persistence across multiple runs."""
    
    config = Config()
    config.browser.user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    config.browser.profile_name = "Profile 2"
    
    session_id = "persistent_session"
    session_manager = BrowserSessionManager(config, session_id=session_id)
    
    try:
        print("🔗 Connecting to browser...")
        connected = await session_manager.connect_to_existing_browser()
        
        if not connected:
            connected = await session_manager.launch_new_browser()
        
        if not connected:
            print("❌ Failed to connect to browser")
            return
        
        print("✅ Connected!")
        
        engine = AutomationEngine(session_manager)
        await engine.initialize()
        
        # Check if we have stored auth tokens
        stored_token = session_manager.get_auth_token("example_token")
        if stored_token:
            print(f"🔑 Found stored auth token: {stored_token[:10]}...")
        else:
            print("🔑 No stored auth token found, creating one...")
            # Simulate storing an auth token
            session_manager.store_auth_token("example_token", "abc123xyz789")
        
        print("\n🌐 Navigating to a website...")
        await engine.navigate_to_url("https://httpbin.org/cookies")
        
        print("\n🍪 Setting a test cookie...")
        await engine.execute_javascript("""
            document.cookie = "test_session=persistent_value; path=/";
        """)
        
        print("\n💾 Saving session state...")
        await session_manager.save_session_state()
        
        print(f"""
✅ Session persistence example completed!

Session ID: {session_id}
- Cookies have been saved
- Auth tokens have been stored
- Local storage will be restored on next visit

Run this script again to see the persistent data restored!
        """)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        if 'engine' in locals():
            await engine.close()
        await session_manager.close()


async def main():
    """Main function to run examples."""
    
    print("🤖 Browser Session Automation Examples")
    print("=" * 50)
    
    # Check for required environment variables
    if not any([
        os.getenv("OPENAI_API_KEY"),
        os.getenv("ANTHROPIC_API_KEY"), 
        os.getenv("GEMINI_API_KEY")
    ]):
        print("⚠️  Warning: No LLM API key found!")
        print("Please set one of: OPENAI_API_KEY, ANTHROPIC_API_KEY, or GEMINI_API_KEY")
        print("Some examples may not work without an API key.")
        print()
    
    examples = [
        ("1", "Basic Usage", basic_example),
        ("2", "Form Filling", form_filling_example),
        ("3", "Session Persistence", session_persistence_example),
    ]
    
    print("Available examples:")
    for num, name, _ in examples:
        print(f"  {num}. {name}")
    
    choice = input("\nEnter example number (1-3) or 'all' to run all: ").strip()
    
    if choice.lower() == 'all':
        for num, name, func in examples:
            print(f"\n{'='*20} {name} {'='*20}")
            await func()
            print(f"{'='*50}")
    else:
        for num, name, func in examples:
            if choice == num:
                print(f"\n{'='*20} {name} {'='*20}")
                await func()
                break
        else:
            print("Invalid choice!")


if __name__ == "__main__":
    asyncio.run(main())
